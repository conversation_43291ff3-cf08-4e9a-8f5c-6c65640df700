package com.erroranalysis.app.ui.study

/**
 * 卡組篩選條件
 */
data class DeckFilter(
    val keyword: String = "",
    val showStarred: <PERSON>olean = false,
    val showNotStarred: Boolean = false,
    val showEmpty: <PERSON>olean = false,
    val showFewCards: <PERSON><PERSON><PERSON> = false,
    val showManyCards: <PERSON><PERSON><PERSON> = false
) {
    /**
     * 檢查是否有任何篩選條件
     */
    fun hasFilter(): Boolean {
        return keyword.isNotEmpty() || 
               showStarred || showNotStarred ||
               showEmpty || showFewCards || showManyCards
    }
    
    /**
     * 檢查卡組是否符合篩選條件
     */
    fun matches(deck: SimpleDeck): Boolean {
        // 關鍵字篩選
        if (keyword.isNotEmpty()) {
            val keywordLower = keyword.lowercase()
            if (!deck.name.lowercase().contains(keywordLower) && 
                !deck.description.lowercase().contains(keywordLower)) {
                return false
            }
        }
        
        // 星星篩選
        if (showStarred || showNotStarred) {
            val starMatches = when {
                showStarred && showNotStarred -> true // 兩個都選，顯示全部
                showStarred -> deck.isStarred
                showNotStarred -> !deck.isStarred
                else -> true
            }
            if (!starMatches) return false
        }
        
        // 卡片數量篩選
        if (showEmpty || showFewCards || showManyCards) {
            val countMatches = when {
                showEmpty && deck.cardCount == 0 -> true
                showFewCards && deck.cardCount in 1..10 -> true
                showManyCards && deck.cardCount > 10 -> true
                else -> false
            }
            if (!countMatches) return false
        }
        
        return true
    }
}

/**
 * 卡組排序類型
 */
enum class DeckSortType(val displayName: String) {
    CREATED_TIME_DESC("創建時間（新到舊）"),
    CREATED_TIME_ASC("創建時間（舊到新）"),
    STARRED_FIRST("重要優先"),
    NAME_ASC("名稱（A-Z）"),
    NAME_DESC("名稱（Z-A）"),
    CARD_COUNT_DESC("卡片數量（多到少）"),
    CARD_COUNT_ASC("卡片數量（少到多）")
}
