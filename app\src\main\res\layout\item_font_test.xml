<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="16dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 字體名稱 -->
        <TextView
            android:id="@+id/text_font_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="字體名稱"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/primary_blue"
            android:layout_marginBottom="12dp" />

        <!-- 分隔線 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/divider_color"
            android:layout_marginBottom="12dp" />

        <!-- 字體樣本 -->
        <TextView
            android:id="@+id/text_sample"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="字體樣本文字"
            android:textSize="14sp"
            android:textColor="@color/text_primary"
            android:lineSpacingExtra="4dp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
