<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res"><file name="bottom_nav_color" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\color\bottom_nav_color.xml" qualifiers="" type="color"/><file name="bg_card_content" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\bg_card_content.xml" qualifiers="" type="drawable"/><file name="bg_hint_chip" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\bg_hint_chip.xml" qualifiers="" type="drawable"/><file name="btn_crop_circle" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\btn_crop_circle.xml" qualifiers="" type="drawable"/><file name="btn_save_circle" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\btn_save_circle.xml" qualifiers="" type="drawable"/><file name="camera_overlay_border" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\camera_overlay_border.xml" qualifiers="" type="drawable"/><file name="capture_button_background" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\capture_button_background.xml" qualifiers="" type="drawable"/><file name="chip_background" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\chip_background.xml" qualifiers="" type="drawable"/><file name="circle_button_background" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\circle_button_background.xml" qualifiers="" type="drawable"/><file name="circle_button_inward" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\circle_button_inward.xml" qualifiers="" type="drawable"/><file name="circle_number_background" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\circle_number_background.xml" qualifiers="" type="drawable"/><file name="deck_cover_gradient" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\deck_cover_gradient.xml" qualifiers="" type="drawable"/><file name="focus_indicator_capturing" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\focus_indicator_capturing.xml" qualifiers="" type="drawable"/><file name="focus_indicator_too_close" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\focus_indicator_too_close.xml" qualifiers="" type="drawable"/><file name="focus_indicator_too_far" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\focus_indicator_too_far.xml" qualifiers="" type="drawable"/><file name="gradient_primary" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\gradient_primary.xml" qualifiers="" type="drawable"/><file name="ic_add" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_ai" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_ai.xml" qualifiers="" type="drawable"/><file name="ic_app_icon" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_app_icon.xml" qualifiers="" type="drawable"/><file name="ic_archive" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_archive.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_book" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_book.xml" qualifiers="" type="drawable"/><file name="ic_camera" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_camera.xml" qualifiers="" type="drawable"/><file name="ic_chart" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_chart.xml" qualifiers="" type="drawable"/><file name="ic_check" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_check.xml" qualifiers="" type="drawable"/><file name="ic_check_circle" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_check_circle.xml" qualifiers="" type="drawable"/><file name="ic_clock" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_clock.xml" qualifiers="" type="drawable"/><file name="ic_close" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_close.xml" qualifiers="" type="drawable"/><file name="ic_copy" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_copy.xml" qualifiers="" type="drawable"/><file name="ic_crop_circle" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_crop_circle.xml" qualifiers="" type="drawable"/><file name="ic_delete" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_edit" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_edit.xml" qualifiers="" type="drawable"/><file name="ic_empty_deck" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_empty_deck.xml" qualifiers="" type="drawable"/><file name="ic_export" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_export.xml" qualifiers="" type="drawable"/><file name="ic_favorite_filled" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_favorite_filled.xml" qualifiers="" type="drawable"/><file name="ic_filter" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_filter.xml" qualifiers="" type="drawable"/><file name="ic_folder" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_folder.xml" qualifiers="" type="drawable"/><file name="ic_home" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_home.xml" qualifiers="" type="drawable"/><file name="ic_image" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_image.xml" qualifiers="" type="drawable"/><file name="ic_info" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_info.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_lightbulb" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_lightbulb.xml" qualifiers="" type="drawable"/><file name="ic_ocr" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_ocr.xml" qualifiers="" type="drawable"/><file name="ic_person" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_person.xml" qualifiers="" type="drawable"/><file name="ic_photo_library" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_photo_library.xml" qualifiers="" type="drawable"/><file name="ic_rotate_90" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_rotate_90.xml" qualifiers="" type="drawable"/><file name="ic_save_circle" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_save_circle.xml" qualifiers="" type="drawable"/><file name="ic_scissors" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_scissors.xml" qualifiers="" type="drawable"/><file name="ic_search" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_sort" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_sort.xml" qualifiers="" type="drawable"/><file name="ic_statistics" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_statistics.xml" qualifiers="" type="drawable"/><file name="scrollbar_thumb" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\scrollbar_thumb.xml" qualifiers="" type="drawable"/><file name="scrollbar_track" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\scrollbar_track.xml" qualifiers="" type="drawable"/><file name="search_background" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\search_background.xml" qualifiers="" type="drawable"/><file name="activity_batch_import" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\activity_batch_import.xml" qualifiers="" type="layout"/><file name="activity_camera" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\activity_camera.xml" qualifiers="" type="layout"/><file name="activity_card_edit" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\activity_card_edit.xml" qualifiers="" type="layout"/><file name="activity_card_viewer" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\activity_card_viewer.xml" qualifiers="" type="layout"/><file name="activity_crop_overlay_test" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\activity_crop_overlay_test.xml" qualifiers="" type="layout"/><file name="activity_deck_detail" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\activity_deck_detail.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_photo_edit" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\activity_photo_edit.xml" qualifiers="" type="layout"/><file name="activity_settings" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\activity_settings.xml" qualifiers="" type="layout"/><file name="activity_simple_study" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\activity_simple_study.xml" qualifiers="" type="layout"/><file name="dialog_card_filter" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\dialog_card_filter.xml" qualifiers="" type="layout"/><file name="dialog_create_card" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\dialog_create_card.xml" qualifiers="" type="layout"/><file name="dialog_create_deck" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\dialog_create_deck.xml" qualifiers="" type="layout"/><file name="dialog_create_deck_simple" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\dialog_create_deck_simple.xml" qualifiers="" type="layout"/><file name="dialog_simple_create_deck" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\dialog_simple_create_deck.xml" qualifiers="" type="layout"/><file name="item_color" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\item_color.xml" qualifiers="" type="layout"/><file name="item_icon_selector" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\item_icon_selector.xml" qualifiers="" type="layout"/><file name="item_quick_template" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\item_quick_template.xml" qualifiers="" type="layout"/><file name="item_simple_deck" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\item_simple_deck.xml" qualifiers="" type="layout"/><file name="item_study_card" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\item_study_card.xml" qualifiers="" type="layout"/><file name="item_template" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\item_template.xml" qualifiers="" type="layout"/><file name="item_theme" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\layout\item_theme.xml" qualifiers="" type="layout"/><file name="bottom_navigation" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\menu\bottom_navigation.xml" qualifiers="" type="menu"/><file name="menu_main" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\menu\menu_main.xml" qualifiers="" type="menu"/><file name="menu_study_main" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\menu\menu_study_main.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\values\colors.xml" qualifiers=""><color name="primary_blue">#667eea</color><color name="primary_purple">#764ba2</color><color name="primary_gradient_start">#667eea</color><color name="primary_gradient_end">#764ba2</color><color name="success_green">#27ae60</color><color name="error_red">#e74c3c</color><color name="warning_orange">#f39c12</color><color name="warning_yellow">#f1c40f</color><color name="info_blue">#3498db</color><color name="background_light">#f5f7fa</color><color name="background_white">#ffffff</color><color name="background_card">#ffffff</color><color name="background_section">#f8f9fa</color><color name="text_primary">#2c3e50</color><color name="text_secondary">#7f8c8d</color><color name="text_hint">#95a5a6</color><color name="text_white">#ffffff</color><color name="border_light">#ecf0f1</color><color name="border_medium">#bdc3c7</color><color name="border_dark">#95a5a6</color><color name="selection_active">#e74c3c</color><color name="selection_inactive">#667eea</color><color name="selection_background_active">#33e74c3c</color><color name="selection_background_inactive">#33667eea</color><color name="detected_area_border">#95a5a6</color><color name="detected_area_background">#1a95a5a6</color><color name="success_background">#e8f5e8</color><color name="error_background">#fff5f5</color><color name="warning_background">#fff3cd</color><color name="info_background">#e8f4fd</color><color name="success_text">#155724</color><color name="error_text">#721c24</color><color name="warning_text">#856404</color><color name="info_text">#0c5460</color><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="background_primary">#F8F9FA</color><color name="background_secondary">#FFFFFF</color><color name="text_tertiary">#9CA3AF</color><color name="ripple_color">#1F000000</color><color name="white_80">#CCFFFFFF</color><color name="primary">#667eea</color><color name="stroke_light">#E5E7EB</color><color name="template_math">#4A90E2</color><color name="template_physics">#F5A623</color><color name="template_chemistry">#7ED321</color><color name="template_biology">#50E3C2</color><color name="template_english">#D0021B</color><color name="template_classic">#8B572A</color><color name="template_modern">#9013FE</color><color name="template_minimal">#6B7280</color><color name="overlay_dark">#80000000</color><color name="overlay_light">#40ffffff</color><color name="camera_background">#000000</color><color name="camera_overlay_border">#ffffff</color><color name="focus_indicator_capturing">#8027ae60</color><color name="focus_indicator_too_close">#80e74c3c</color><color name="focus_indicator_too_far">#80f1c40f</color><color name="document_boundary">#ff0000</color><color name="document_corner">#ff0000</color><color name="document_fill">#********</color><color name="document_boundary_preview">#ff0000</color></file><file path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">智學分析</string><string name="main_title">智學分析</string><string name="main_subtitle">讓錯題成為進步的階梯</string><string name="menu_camera">拍照分析</string><string name="menu_error_bank">錯題庫</string><string name="menu_report">學習報告</string><string name="menu_practice">練習推薦</string><string name="today_learning">今日學習</string><string name="analyzed_questions">已分析題目</string><string name="error_count">錯題數量</string><string name="recent_analysis">最近分析</string><string name="camera_title">拍照識別</string><string name="camera_hint_capturing">擷取中...</string><string name="camera_hint_too_close">遠一點</string><string name="camera_hint_too_far">近一點</string><string name="camera_instruction">請將試卷放在框內，確保文字清晰可見</string><string name="selection_title">選擇題目</string><string name="selection_mode">選擇</string><string name="add_selection">新增</string><string name="selection_instruction">拖拽方框移動位置，拖拽四個角調整大小，系統會自動識別完整題目內容</string><string name="retake_photo">重新拍攝</string><string name="confirm_selection">確認選擇</string><string name="confirm_selection_count">確認選擇 (%d)</string><string name="preview_title">確認題目</string><string name="selected_questions">已選擇的題目</string><string name="question_number">題目 %d</string><string name="student_answer">學生答案：</string><string name="selection_tips_title">選擇提示</string><string name="selection_tip_1">紅色框表示已選擇要分析的題目</string><string name="selection_tip_2">藍色框表示可選擇的題目</string><string name="selection_tip_3">點擊 + 號可添加題目到分析列表</string><string name="reselect">重新選擇</string><string name="start_analysis">開始分析</string><string name="marking_title">錯題標記</string><string name="answer_check">答案檢查</string><string name="marking_instruction">請標記哪些題目答錯了</string><string name="correct_answer">正確答案：</string><string name="marking_tip">點擊 ✗ 標記為錯題，點擊 ? 表示不確定，系統會重新檢查</string><string name="manual_edit">手動編輯</string><string name="analysis_title">錯題分析</string><string name="question_analysis">題目 %d 分析</string><string name="question_label">題目：</string><string name="error_analysis">錯誤分析：</string><string name="knowledge_point">知識點：</string><string name="learning_suggestion">學習建議：</string><string name="save_to_bank">保存到錯題庫</string><string name="error_bank_title">錯題庫</string><string name="my_errors">我的錯題</string><string name="filter">篩選</string><string name="all_subjects">全部</string><string name="math">數學</string><string name="physics">物理</string><string name="chemistry">化學</string><string name="calculation_error">計算錯誤</string><string name="concept_error">概念錯誤</string><string name="formula_application">公式應用</string><string name="study_deck_title">錯題庫</string><string name="create_deck">建立卡組</string><string name="edit_deck">編輯卡組</string><string name="deck_name">卡組名稱</string><string name="deck_description">描述</string><string name="deck_subject">科目</string><string name="deck_tags">標籤</string><string name="today_review">今日複習</string><string name="statistics">統計</string><string name="search_decks">搜尋卡組...</string><string name="no_decks_title">還沒有卡組</string><string name="no_decks_message">點擊右下角的 + 按鈕建立第一個卡組</string><string name="last_study">最後學習: %s</string><string name="card_count">%d 張</string><string name="progress_percent">%d%%</string><string name="subject_templates">學科模板</string><string name="style_templates">風格模板</string><string name="exam_templates">考試模板</string><string name="quick_templates">快速建立</string><string name="deck_created">卡組建立成功</string><string name="deck_updated">卡組更新成功</string><string name="deck_deleted">卡組已刪除</string><string name="delete_deck_confirm">確定要刪除「%s」嗎？此操作無法復原。</string><string name="duplicate">複製</string><string name="archive">封存</string><string name="export">匯出</string><string name="report_title">學習報告</string><string name="weekly_stats">本週學習統計</string><string formatted="false" name="date_range">%s - %s</string><string name="analyzed_count">分析題目</string><string name="error_questions">錯題數量</string><string name="accuracy_rate">正確率</string><string name="review_times">複習次數</string><string name="error_distribution">錯題分布</string><string name="learning_suggestions">學習建議</string><string name="progress_obvious">進步明顯</string><string name="accuracy_improved">本週正確率比上週提升了%d%%</string><string name="need_improvement">需要加強</string><string name="practice_suggestion">建議多練習%s相關題目</string><string name="export_report">導出報告</string><string name="settings_title">設置</string><string name="profile">個人資料</string><string name="edit_profile">編輯資料</string><string name="ai_settings">AI 設置</string><string name="gemini_api_key">Gemini API Key</string><string name="api_key_hint">請輸入您的 Gemini 2.5 Flash API Key</string><string name="api_key_description">用於題目解析和文字識別，請到 Google AI Studio 獲取</string><string name="api_connection_normal">API 連接正常</string><string name="last_test_time">上次測試：%s</string><string name="test_api_connection">測試 API 連接</string><string name="app_settings">應用設置</string><string name="push_notifications">推送通知</string><string name="push_description">接收學習提醒和分析結果</string><string name="auto_save">自動保存</string><string name="auto_save_description">自動保存分析結果到錯題庫</string><string name="dark_mode">深色模式</string><string name="dark_mode_description">護眼模式，適合夜間使用</string><string name="other_settings">其他</string><string name="help_support">幫助與支援</string><string name="privacy_policy">隱私政策</string><string name="about_us">關於我們</string><string name="logout">登出</string><string name="nav_home">首頁</string><string name="nav_camera">拍照</string><string name="nav_error_bank">錯題庫</string><string name="nav_profile">我的</string><string name="ok">確定</string><string name="cancel">取消</string><string name="save">保存</string><string name="delete">刪除</string><string name="edit">編輯</string><string name="loading">載入中...</string><string name="error">錯誤</string><string name="success">成功</string><string name="retry">重試</string><string name="permission_camera_title">需要相機權限</string><string name="permission_camera_message">此應用需要相機權限來拍攝試卷</string><string name="permission_storage_title">需要存儲權限</string><string name="permission_storage_message">此應用需要存儲權限來保存圖片</string><string name="permission_denied">權限被拒絕</string><string name="go_to_settings">前往設置</string></file><file path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.ErrorAnalysisApp" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_purple</item>
        <item name="colorOnPrimary">@color/text_white</item>

        <item name="colorSecondary">@color/success_green</item>
        <item name="colorSecondaryVariant">@color/info_blue</item>
        <item name="colorOnSecondary">@color/text_white</item>

        <item name="android:colorBackground">@color/background_light</item>
        <item name="colorOnBackground">@color/text_primary</item>

        <item name="colorSurface">@color/background_white</item>
        <item name="colorOnSurface">@color/text_primary</item>

        <item name="colorError">@color/error_red</item>
        <item name="colorOnError">@color/text_white</item>

        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
    </style></file><file name="backup_rules" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="bg_tag_rounded" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\bg_tag_rounded.xml" qualifiers="" type="drawable"/><file name="ic_expand_less" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_expand_less.xml" qualifiers="" type="drawable"/><file name="ic_expand_more" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_expand_more.xml" qualifiers="" type="drawable"/><file name="ic_palette" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_palette.xml" qualifiers="" type="drawable"/><file name="circle_background_primary" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\circle_background_primary.xml" qualifiers="" type="drawable"/><file name="ic_filter_active" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_filter_active.xml" qualifiers="" type="drawable"/><file name="ic_filter_inactive" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_filter_inactive.xml" qualifiers="" type="drawable"/><file name="ic_ai_white" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\drawable\ic_ai_white.xml" qualifiers="" type="drawable"/><file path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="button_icon_size">18dp</dimen><dimen name="card_corner_radius">12dp</dimen><dimen name="button_corner_radius">8dp</dimen><dimen name="spacing_small">8dp</dimen><dimen name="spacing_medium">16dp</dimen><dimen name="spacing_large">24dp</dimen></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>