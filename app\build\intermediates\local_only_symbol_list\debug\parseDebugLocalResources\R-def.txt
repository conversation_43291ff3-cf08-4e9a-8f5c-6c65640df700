R_DEF: Internal format may change without notice
local
color background_card
color background_light
color background_primary
color background_secondary
color background_section
color background_white
color black
color border_dark
color border_light
color border_medium
color bottom_nav_color
color camera_background
color camera_overlay_border
color detected_area_background
color detected_area_border
color document_boundary
color document_boundary_preview
color document_corner
color document_fill
color error_background
color error_red
color error_text
color focus_indicator_capturing
color focus_indicator_too_close
color focus_indicator_too_far
color info_background
color info_blue
color info_text
color overlay_dark
color overlay_light
color primary
color primary_blue
color primary_gradient_end
color primary_gradient_start
color primary_purple
color purple_200
color purple_500
color purple_700
color ripple_color
color selection_active
color selection_background_active
color selection_background_inactive
color selection_inactive
color stroke_light
color success_background
color success_green
color success_text
color teal_200
color teal_700
color template_biology
color template_chemistry
color template_classic
color template_english
color template_math
color template_minimal
color template_modern
color template_physics
color text_hint
color text_primary
color text_secondary
color text_tertiary
color text_white
color warning_background
color warning_orange
color warning_text
color warning_yellow
color white
color white_80
dimen button_corner_radius
dimen button_icon_size
dimen card_corner_radius
dimen spacing_large
dimen spacing_medium
dimen spacing_small
drawable bg_card_content
drawable bg_hint_chip
drawable bg_tag_rounded
drawable btn_crop_circle
drawable btn_save_circle
drawable camera_overlay_border
drawable capture_button_background
drawable chip_background
drawable circle_background_primary
drawable circle_button_background
drawable circle_button_inward
drawable circle_number_background
drawable deck_cover_gradient
drawable focus_indicator_capturing
drawable focus_indicator_too_close
drawable focus_indicator_too_far
drawable gradient_primary
drawable ic_add
drawable ic_ai
drawable ic_ai_white
drawable ic_app_icon
drawable ic_archive
drawable ic_arrow_back
drawable ic_book
drawable ic_camera
drawable ic_chart
drawable ic_check
drawable ic_check_circle
drawable ic_clock
drawable ic_close
drawable ic_copy
drawable ic_crop_circle
drawable ic_delete
drawable ic_edit
drawable ic_empty_deck
drawable ic_expand_less
drawable ic_expand_more
drawable ic_export
drawable ic_favorite_filled
drawable ic_filter
drawable ic_filter_active
drawable ic_filter_inactive
drawable ic_folder
drawable ic_home
drawable ic_image
drawable ic_info
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_lightbulb
drawable ic_ocr
drawable ic_palette
drawable ic_person
drawable ic_photo_library
drawable ic_rotate_90
drawable ic_save_circle
drawable ic_scissors
drawable ic_search
drawable ic_settings
drawable ic_sort
drawable ic_statistics
drawable scrollbar_thumb
drawable scrollbar_track
drawable search_background
id action_archived
id action_filter
id action_settings
id action_sort
id bottom_navigation
id btn_apply
id btn_back
id btn_camera
id btn_cancel
id btn_capture
id btn_clear
id btn_confirm
id btn_crop
id btn_crop_adjustment
id btn_edit
id btn_error_bank
id btn_filter
id btn_gallery
id btn_logs
id btn_perspective_correction
id btn_practice
id btn_report
id btn_reset
id btn_settings
id btn_toggle
id button_add_answer_image
id button_add_question_image
id button_ai_answer
id button_cancel
id button_complete
id button_create
id button_ocr_answer
id button_ocr_question
id button_rotate_question_image
id button_save
id button_select_file
id button_show_example
id button_view_deck
id card_container
id card_icon
id card_preview
id card_template
id card_template_preview
id card_theme
id chip_beginner
id chip_familiar
id chip_group_mastery
id chip_group_tags
id chip_learning
id chip_mastered
id chip_not_learned
id chip_proficient
id content_frame
id crop_overlay
id crop_selection_overlay
id document_boundary_overlay
id edit_ai_answer
id edit_answer
id edit_deck_name
id edit_description
id edit_keyword
id edit_question
id edit_subject
id edit_tags
id fab_create_card
id fab_create_deck
id icon_selected
id icon_theme_expand
id indicator_status
id iv_photo
id layout_ai_answer
id layout_bottom_controls
id layout_camera_controls
id layout_confirm_buttons
id layout_custom
id layout_empty
id layout_header
id layout_photo_container
id layout_quick
id layout_selection_buttons
id layout_theme_option
id layout_top_toolbar
id nav_camera
id nav_error_bank
id nav_home
id nav_profile
id nav_settings
id progress_bar
id progress_mastery
id recycler_cards
id recycler_colors
id recycler_decks
id recycler_exam_templates
id recycler_icons
id recycler_quick_templates
id recycler_style_templates
id recycler_subject_templates
id recycler_themes
id scroll_view
id tab_layout
id test_image
id text_answer
id text_card_count
id text_card_count_number
id text_cover_icon
id text_created_time
id text_current_theme
id text_deck_name
id text_deck_title
id text_difficulty
id text_errors
id text_icon
id text_icon_name
id text_instructions
id text_mastery_level
id text_preview_count
id text_preview_icon
id text_preview_name
id text_question
id text_stats
id text_status
id text_tags
id text_template_description
id text_template_icon
id text_template_name
id text_template_subject
id text_template_tags
id text_theme_description
id text_theme_emoji
id text_theme_name
id toolbar
id tv_analyzed_count
id tv_content
id tv_content_type
id tv_error_count
id tv_focus_indicator
id tv_instruction
id tv_tap_hint
id tv_title
id viewFinder
id view_color
id view_color_border
id view_theme_preview
layout activity_batch_import
layout activity_camera
layout activity_card_edit
layout activity_card_viewer
layout activity_crop_overlay_test
layout activity_deck_detail
layout activity_main
layout activity_photo_edit
layout activity_settings
layout activity_simple_study
layout dialog_card_filter
layout dialog_create_card
layout dialog_create_deck
layout dialog_create_deck_simple
layout dialog_simple_create_deck
layout item_color
layout item_icon_selector
layout item_quick_template
layout item_simple_deck
layout item_study_card
layout item_template
layout item_theme
menu bottom_navigation
menu menu_main
menu menu_study_main
mipmap ic_launcher
mipmap ic_launcher_round
string about_us
string accuracy_improved
string accuracy_rate
string add_selection
string ai_settings
string all_subjects
string analysis_title
string analyzed_count
string analyzed_questions
string answer_check
string api_connection_normal
string api_key_description
string api_key_hint
string app_name
string app_settings
string archive
string auto_save
string auto_save_description
string calculation_error
string camera_hint_capturing
string camera_hint_too_close
string camera_hint_too_far
string camera_instruction
string camera_title
string cancel
string card_count
string chemistry
string concept_error
string confirm_selection
string confirm_selection_count
string correct_answer
string create_deck
string dark_mode
string dark_mode_description
string date_range
string deck_created
string deck_deleted
string deck_description
string deck_name
string deck_subject
string deck_tags
string deck_updated
string delete
string delete_deck_confirm
string duplicate
string edit
string edit_deck
string edit_profile
string error
string error_analysis
string error_bank_title
string error_count
string error_distribution
string error_questions
string exam_templates
string export
string export_report
string filter
string formula_application
string gemini_api_key
string go_to_settings
string help_support
string knowledge_point
string last_study
string last_test_time
string learning_suggestion
string learning_suggestions
string loading
string logout
string main_subtitle
string main_title
string manual_edit
string marking_instruction
string marking_tip
string marking_title
string math
string menu_camera
string menu_error_bank
string menu_practice
string menu_report
string my_errors
string nav_camera
string nav_error_bank
string nav_home
string nav_profile
string need_improvement
string no_decks_message
string no_decks_title
string ok
string other_settings
string permission_camera_message
string permission_camera_title
string permission_denied
string permission_storage_message
string permission_storage_title
string physics
string practice_suggestion
string preview_title
string privacy_policy
string profile
string progress_obvious
string progress_percent
string push_description
string push_notifications
string question_analysis
string question_label
string question_number
string quick_templates
string recent_analysis
string report_title
string reselect
string retake_photo
string retry
string review_times
string save
string save_to_bank
string search_decks
string selected_questions
string selection_instruction
string selection_mode
string selection_tip_1
string selection_tip_2
string selection_tip_3
string selection_tips_title
string selection_title
string settings_title
string start_analysis
string statistics
string student_answer
string study_deck_title
string style_templates
string subject_templates
string success
string test_api_connection
string today_learning
string today_review
string weekly_stats
style Theme.ErrorAnalysisApp
xml backup_rules
xml data_extraction_rules
