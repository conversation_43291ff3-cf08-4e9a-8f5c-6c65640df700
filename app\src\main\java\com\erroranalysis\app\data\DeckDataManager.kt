package com.erroranalysis.app.data

import android.content.Context
import android.content.SharedPreferences
import com.erroranalysis.app.ui.study.SimpleDeck
import com.erroranalysis.app.ui.study.StudyCard
import com.erroranalysis.app.ui.study.CardDifficulty
import com.erroranalysis.app.utils.ImageStorageManager
import org.json.JSONArray
import org.json.JSONObject

/**
 * 簡化的卡組資料管理器
 * 使用SharedPreferences進行資料持久化
 */
class DeckDataManager(private val context: Context) {

    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val imageStorageManager by lazy { ImageStorageManager(context) }
    
    companion object {
        private const val PREFS_NAME = "deck_data"
        private const val KEY_DECKS = "decks"
        private const val KEY_CARDS = "cards"
        private const val KEY_NEXT_DECK_ID = "next_deck_id"
        private const val KEY_NEXT_CARD_ID = "next_card_id"

        // 隨手記卡組的固定ID
        const val QUICK_NOTES_DECK_ID = "quick_notes_deck"
    }
    
    /**
     * 保存所有卡組
     */
    fun saveDecks(decks: List<SimpleDeck>) {
        val jsonArray = JSONArray()
        
        decks.forEach { deck ->
            val jsonObject = JSONObject().apply {
                put("id", deck.id)
                put("name", deck.name)
                put("description", deck.description)
                put("icon", deck.icon)
                put("color", deck.color)
                put("cardCount", deck.cardCount)
                put("createdAt", deck.createdAt)
                put("isStarred", deck.isStarred)
            }
            jsonArray.put(jsonObject)
        }
        
        prefs.edit()
            .putString(KEY_DECKS, jsonArray.toString())
            .apply()
    }
    
    /**
     * 載入所有卡組
     */
    fun loadDecks(): MutableList<SimpleDeck> {
        val decksJson = prefs.getString(KEY_DECKS, null)
        val deckList = mutableListOf<SimpleDeck>()
        
        if (decksJson != null) {
            try {
                val jsonArray = JSONArray(decksJson)
                for (i in 0 until jsonArray.length()) {
                    val jsonObject = jsonArray.getJSONObject(i)
                    val deck = SimpleDeck(
                        id = jsonObject.getString("id"),
                        name = jsonObject.getString("name"),
                        description = jsonObject.getString("description"),
                        icon = jsonObject.getString("icon"),
                        color = jsonObject.getString("color"),
                        cardCount = jsonObject.getInt("cardCount"),
                        createdAt = jsonObject.optLong("createdAt", System.currentTimeMillis()),
                        isStarred = jsonObject.optBoolean("isStarred", false)
                    )
                    deckList.add(deck)
                }
            } catch (e: Exception) {
                e.printStackTrace()
                // 如果解析失敗，返回空列表
                return mutableListOf()
            }
        } else {
            // 第一次使用，檢查是否需要初始化預設資料
            return initializeDefaultDecks()
        }

        // 確保隨手記卡組存在
        val hasQuickNotes = deckList.any { it.id == QUICK_NOTES_DECK_ID }
        if (!hasQuickNotes) {
            val quickNotesDeck = SimpleDeck(QUICK_NOTES_DECK_ID, "隨手記", "臨時記住想到的點", "📝", "#4A90E2", 0, System.currentTimeMillis(), false)
            deckList.add(0, quickNotesDeck) // 添加到列表開頭
            saveDecks(deckList)
        }

        return deckList
    }
    
    /**
     * 添加新卡組
     */
    fun addDeck(deck: SimpleDeck) {
        val currentDecks = loadDecks()
        currentDecks.add(deck)
        saveDecks(currentDecks)
    }
    
    /**
     * 更新卡組
     */
    fun updateDeck(updatedDeck: SimpleDeck) {
        val currentDecks = loadDecks()
        val index = currentDecks.indexOfFirst { it.id == updatedDeck.id }
        if (index != -1) {
            currentDecks[index] = updatedDeck
            saveDecks(currentDecks)
        }
    }
    
    /**
     * 刪除卡組
     */
    fun deleteDeck(deckId: String) {
        // 防止刪除隨手記卡組
        if (deckId == QUICK_NOTES_DECK_ID) {
            android.util.Log.w("DeckDataManager", "嘗試刪除隨手記卡組，操作被阻止")
            return
        }

        val currentDecks = loadDecks()
        currentDecks.removeAll { it.id == deckId }
        saveDecks(currentDecks)
    }
    
    /**
     * 生成新的卡組ID
     */
    fun generateNewDeckId(): String {
        // 從1000開始，避免與預設卡組ID衝突
        val nextId = prefs.getLong(KEY_NEXT_DECK_ID, 1000)
        prefs.edit()
            .putLong(KEY_NEXT_DECK_ID, nextId + 1)
            .apply()
        return "deck_$nextId"
    }

    /**
     * 獲取隨手記卡組
     */
    fun getQuickNotesDeck(): SimpleDeck? {
        val decks = loadDecks()
        return decks.find { it.id == QUICK_NOTES_DECK_ID }
    }

    /**
     * 檢查是否為隨手記卡組
     */
    fun isQuickNotesDeck(deckId: String): Boolean {
        return deckId == QUICK_NOTES_DECK_ID
    }

    /**
     * 生成新的卡片ID
     */
    fun generateNewCardId(): String {
        // 從10000開始，確保唯一性
        val nextId = prefs.getLong(KEY_NEXT_CARD_ID, 10000)
        prefs.edit()
            .putLong(KEY_NEXT_CARD_ID, nextId + 1)
            .apply()
        return "card_$nextId"
    }
    
    /**
     * 初始化預設卡組資料（只在第一次使用時）
     */
    private fun initializeDefaultDecks(): MutableList<SimpleDeck> {
        val isFirstTime = prefs.getBoolean("is_first_time", true)

        return if (isFirstTime) {
            // 第一次使用，建立"隨手記"預設卡組
            val defaultDecks = mutableListOf(
                SimpleDeck(QUICK_NOTES_DECK_ID, "隨手記", "臨時記住想到的點", "📝", "#4A90E2", 0, System.currentTimeMillis(), false)
            )

            // 保存預設卡組
            saveDecks(defaultDecks)

            // 標記不再是第一次使用
            prefs.edit().putBoolean("is_first_time", false).apply()

            defaultDecks
        } else {
            // 不是第一次使用，返回空列表讓loadDecks正常處理
            mutableListOf()
        }
    }
    
    // ==================== 卡片管理功能 ====================

    /**
     * 保存所有卡片
     */
    fun saveCards(cards: List<StudyCard>) {
        val jsonArray = JSONArray()

        cards.forEach { card ->
            val jsonObject = JSONObject().apply {
                put("id", card.id)
                put("deckId", card.deckId)
                put("question", card.question)
                put("answer", card.answer)
                put("aiAnswer", card.aiAnswer)
                put("questionImagePath", card.questionImagePath ?: "")
                put("answerImagePath", card.answerImagePath ?: "")
                put("tags", JSONArray(card.tags))
                put("difficulty", card.difficulty.name)
                put("masteryLevel", card.masteryLevel)
                put("reviewCount", card.reviewCount)
                put("correctCount", card.correctCount)
                put("lastReviewTime", card.lastReviewTime)
                put("nextReviewTime", card.nextReviewTime)
                put("createdTime", card.createdTime)
                put("updatedTime", card.updatedTime)
                put("isStarred", card.isStarred)
            }
            jsonArray.put(jsonObject)
        }

        prefs.edit()
            .putString(KEY_CARDS, jsonArray.toString())
            .apply()
    }

    /**
     * 載入所有卡片
     */
    fun loadCards(): MutableList<StudyCard> {
        val cardsJson = prefs.getString(KEY_CARDS, null)
        val cardList = mutableListOf<StudyCard>()

        if (cardsJson != null) {
            try {
                val jsonArray = JSONArray(cardsJson)
                for (i in 0 until jsonArray.length()) {
                    val jsonObject = jsonArray.getJSONObject(i)

                    // 解析標籤
                    val tagsArray = jsonObject.getJSONArray("tags")
                    val tags = mutableListOf<String>()
                    for (j in 0 until tagsArray.length()) {
                        tags.add(tagsArray.getString(j))
                    }

                    val card = StudyCard(
                        id = jsonObject.getString("id"),
                        deckId = jsonObject.getString("deckId"),
                        question = jsonObject.getString("question"),
                        answer = jsonObject.getString("answer"),
                        aiAnswer = jsonObject.optString("aiAnswer", ""),
                        questionImagePath = jsonObject.getString("questionImagePath").takeIf { it.isNotEmpty() },
                        answerImagePath = jsonObject.getString("answerImagePath").takeIf { it.isNotEmpty() },
                        tags = tags,
                        difficulty = CardDifficulty.valueOf(jsonObject.getString("difficulty")),
                        masteryLevel = jsonObject.getInt("masteryLevel"),
                        reviewCount = jsonObject.getInt("reviewCount"),
                        correctCount = jsonObject.getInt("correctCount"),
                        lastReviewTime = jsonObject.getLong("lastReviewTime"),
                        nextReviewTime = jsonObject.getLong("nextReviewTime"),
                        createdTime = jsonObject.getLong("createdTime"),
                        updatedTime = jsonObject.getLong("updatedTime"),
                        isStarred = jsonObject.optBoolean("isStarred", false)
                    )
                    cardList.add(card)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        return cardList
    }

    /**
     * 根據卡組ID載入卡片
     */
    fun loadCardsByDeckId(deckId: String): List<StudyCard> {
        return loadCards().filter { it.deckId == deckId }
    }

    /**
     * 添加新卡片
     */
    fun addCard(card: StudyCard) {
        // 使用同步方式避免資料競爭
        synchronized(this) {
            android.util.Log.d("DeckDataManager", "添加卡片 - 卡組ID: ${card.deckId}, 卡片ID: ${card.id}")

            // 驗證卡組是否存在
            val decks = loadDecks()
            val deckExists = decks.any { it.id == card.deckId }
            if (!deckExists) {
                android.util.Log.e("DeckDataManager", "錯誤：嘗試添加卡片到不存在的卡組 ${card.deckId}")
                return
            }

            val currentCards = loadCards()
            currentCards.add(card)
            saveCards(currentCards)

            android.util.Log.d("DeckDataManager", "卡片已保存，總卡片數: ${currentCards.size}")

            // 更新卡組的卡片數量
            updateDeckCardCount(card.deckId)
        }
    }

    /**
     * 更新卡片
     */
    fun updateCard(updatedCard: StudyCard) {
        android.util.Log.d("DeckDataManager", "開始更新卡片: ${updatedCard.id}")
        val currentCards = loadCards()
        val index = currentCards.indexOfFirst { it.id == updatedCard.id }
        if (index != -1) {
            android.util.Log.d("DeckDataManager", "找到卡片，索引: $index")
            currentCards[index] = updatedCard.copy(updatedTime = System.currentTimeMillis())
            saveCards(currentCards)
            android.util.Log.d("DeckDataManager", "卡片已更新並保存")
        } else {
            android.util.Log.e("DeckDataManager", "找不到要更新的卡片: ${updatedCard.id}")
        }
    }

    /**
     * 移動卡片到其他卡組
     */
    fun moveCard(cardId: String, targetDeckId: String) {
        synchronized(this) {
            val currentCards = loadCards()
            val cardToMove = currentCards.find { it.id == cardId }

            if (cardToMove == null) {
                android.util.Log.e("DeckDataManager", "找不到要移動的卡片: $cardId")
                return
            }

            // 驗證目標卡組是否存在
            val decks = loadDecks()
            val targetDeckExists = decks.any { it.id == targetDeckId }
            if (!targetDeckExists) {
                android.util.Log.e("DeckDataManager", "目標卡組不存在: $targetDeckId")
                return
            }

            val originalDeckId = cardToMove.deckId

            // 更新卡片的卡組ID
            val index = currentCards.indexOfFirst { it.id == cardId }
            if (index != -1) {
                currentCards[index] = cardToMove.copy(deckId = targetDeckId)
                saveCards(currentCards)

                // 更新原卡組和目標卡組的卡片數量
                updateDeckCardCount(originalDeckId)
                updateDeckCardCount(targetDeckId)

                android.util.Log.d("DeckDataManager", "卡片 $cardId 已從 $originalDeckId 移動到 $targetDeckId")
            }
        }
    }

    /**
     * 切換卡片星星狀態
     */
    fun toggleCardStar(cardId: String) {
        val currentCards = loadCards()
        val index = currentCards.indexOfFirst { it.id == cardId }
        if (index != -1) {
            val card = currentCards[index]
            currentCards[index] = card.copy(
                isStarred = !card.isStarred,
                updatedTime = System.currentTimeMillis()
            )
            saveCards(currentCards)
            android.util.Log.d("DeckDataManager", "卡片 $cardId 星星狀態已切換為: ${currentCards[index].isStarred}")
        }
    }

    /**
     * 刪除卡片
     */
    fun deleteCard(cardId: String) {
        val currentCards = loadCards()
        val cardToDelete = currentCards.find { it.id == cardId }

        // 清理卡片中的圖片文件
        cardToDelete?.let { card ->
            cleanupCardImages(card.question)
            cleanupCardImages(card.answer)
        }

        currentCards.removeAll { it.id == cardId }
        saveCards(currentCards)

        // 更新卡組的卡片數量
        cardToDelete?.let { updateDeckCardCount(it.deckId) }
    }

    /**
     * 更新卡組的卡片數量
     */
    private fun updateDeckCardCount(deckId: String) {
        // 使用同步方式避免資料競爭
        synchronized(this) {
            val decks = loadDecks()
            val cardCount = loadCardsByDeckId(deckId).size
            val deckIndex = decks.indexOfFirst { it.id == deckId }

            android.util.Log.d("DeckDataManager", "更新卡組 $deckId 的卡片數量: $cardCount")

            if (deckIndex != -1) {
                decks[deckIndex] = decks[deckIndex].copy(cardCount = cardCount)
                saveDecks(decks)
                android.util.Log.d("DeckDataManager", "卡組 $deckId 卡片數量已更新為: $cardCount")
            } else {
                android.util.Log.w("DeckDataManager", "找不到卡組 $deckId")
            }
        }
    }

    /**
     * 驗證資料完整性
     */
    fun validateDataIntegrity(): String {
        val decks = loadDecks()
        val cards = loadCards()
        val report = StringBuilder()

        report.appendLine("=== 資料完整性報告 ===")
        report.appendLine("卡組數量: ${decks.size}")
        report.appendLine("卡片數量: ${cards.size}")

        // 檢查卡組ID重複
        val deckIds = decks.map { it.id }
        val duplicateIds = deckIds.groupBy { it }.filter { it.value.size > 1 }.keys
        if (duplicateIds.isNotEmpty()) {
            report.appendLine("🚨 發現重複的卡組ID:")
            duplicateIds.forEach { id ->
                val duplicateDecks = decks.filter { it.id == id }
                report.appendLine("  ID: $id 被以下卡組使用:")
                duplicateDecks.forEach { deck ->
                    report.appendLine("    - ${deck.name}")
                }
            }
        }

        // 檢查每個卡組的卡片數量
        decks.forEach { deck ->
            val actualCardCount = cards.count { it.deckId == deck.id }
            val recordedCardCount = deck.cardCount

            report.appendLine("卡組 ${deck.name} (${deck.id}):")
            report.appendLine("  記錄的卡片數: $recordedCardCount")
            report.appendLine("  實際卡片數: $actualCardCount")

            if (actualCardCount != recordedCardCount) {
                report.appendLine("  ⚠️ 數量不一致！")
            }
        }

        // 檢查孤兒卡片（沒有對應卡組的卡片）
        val orphanCards = cards.filter { card ->
            decks.none { deck -> deck.id == card.deckId }
        }

        if (orphanCards.isNotEmpty()) {
            report.appendLine("⚠️ 發現 ${orphanCards.size} 張孤兒卡片:")
            orphanCards.forEach { card ->
                report.appendLine("  卡片 ${card.id} 屬於不存在的卡組 ${card.deckId}")
            }
        }

        val reportString = report.toString()
        android.util.Log.d("DeckDataManager", reportString)
        return reportString
    }

    /**
     * 修復資料完整性問題
     */
    fun fixDataIntegrity() {
        synchronized(this) {
            val decks = loadDecks().toMutableList()
            val cards = loadCards().toMutableList()

            android.util.Log.i("DeckDataManager", "開始修復資料完整性...")

            // 1. 修復卡組ID重複問題
            val deckIds = decks.map { it.id }
            val duplicateIds = deckIds.groupBy { it }.filter { it.value.size > 1 }.keys

            if (duplicateIds.isNotEmpty()) {
                android.util.Log.w("DeckDataManager", "發現重複ID，開始修復...")

                duplicateIds.forEach { duplicateId ->
                    val duplicateDecks = decks.filter { it.id == duplicateId }
                    android.util.Log.w("DeckDataManager", "重複ID: $duplicateId, 影響 ${duplicateDecks.size} 個卡組")

                    // 保留第一個，其他的重新分配ID
                    duplicateDecks.drop(1).forEach { deck ->
                        val newId = generateNewDeckId()
                        android.util.Log.i("DeckDataManager", "卡組 '${deck.name}' ID從 ${deck.id} 改為 $newId")

                        // 更新卡組ID
                        val deckIndex = decks.indexOfFirst { it.id == deck.id && it.name == deck.name }
                        if (deckIndex != -1) {
                            decks[deckIndex] = deck.copy(id = newId)
                        }

                        // 更新相關卡片的deckId
                        cards.forEachIndexed { index, card ->
                            if (card.deckId == duplicateId) {
                                // 只更新屬於這個特定卡組的卡片
                                // 這裡我們假設第一個重複的卡組保留原ID
                                val shouldUpdateCard = cards.indexOf(card) >= duplicateDecks.size - 1
                                if (shouldUpdateCard) {
                                    cards[index] = card.copy(deckId = newId)
                                }
                            }
                        }
                    }
                }

                // 保存修復後的資料
                saveDecks(decks)
                saveCards(cards)
            }

            // 2. 移除孤兒卡片
            val validCards = cards.filter { card ->
                decks.any { deck -> deck.id == card.deckId }
            }

            if (validCards.size != cards.size) {
                android.util.Log.w("DeckDataManager", "移除了 ${cards.size - validCards.size} 張孤兒卡片")
                saveCards(validCards)
            }

            // 3. 更新所有卡組的卡片數量
            decks.forEach { deck ->
                updateDeckCardCount(deck.id)
            }

            android.util.Log.i("DeckDataManager", "資料完整性修復完成")
        }
    }

    /**
     * 清理卡片內容中的圖片文件
     */
    private fun cleanupCardImages(content: String) {
        try {
            // 嘗試解析JSON格式的內容
            val contentItems = com.google.gson.Gson().fromJson(
                content,
                Array<ContentItem>::class.java
            )

            contentItems?.forEach { item ->
                if (item.type == "image" && item.content.isNotEmpty()) {
                    imageStorageManager.deleteImage(item.content)
                }
            }
        } catch (e: Exception) {
            // 如果不是JSON格式，忽略
        }
    }

    /**
     * 內容項目資料類別（用於JSON解析）
     */
    private data class ContentItem(
        val type: String,
        val content: String
    )

    /**
     * 清除所有資料（用於測試）
     */
    fun clearAllData() {
        // 清除所有圖片文件
        imageStorageManager.clearAllImages()
        // 清除SharedPreferences資料
        prefs.edit().clear().apply()
    }
}
