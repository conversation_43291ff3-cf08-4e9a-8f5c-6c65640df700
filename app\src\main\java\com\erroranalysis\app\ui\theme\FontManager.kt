package com.erroranalysis.app.ui.theme

import android.content.Context
import android.content.SharedPreferences
import android.graphics.Typeface
import androidx.core.content.res.ResourcesCompat
import com.erroranalysis.app.R

/**
 * 字體管理器
 * 管理應用中的自定義字體
 */
object FontManager {
    
    /**
     * 可用的字體類型
     */
    enum class FontType(val displayName: String, val fontResId: Int?) {
        SYSTEM("系統預設", null),
        CHENYU_LUOYAN("辰宇落雁體", R.font.chenyu_luoyan)
        // 以下字體需要實際的字體文件才能使用
        // 添加字體文件後，取消註釋以下行：
        // NOTO_SANS_CJK("思源黑體", R.font.noto_sans_cjk),
        // QINGSONG_HANDWRITING("清松手寫體", R.font.qingsong_handwriting)
    }
    
    private var currentFontType = FontType.SYSTEM
    private val fontCache = mutableMapOf<FontType, Typeface?>()
    private const val PREFS_NAME = "font_settings"
    private const val KEY_CURRENT_FONT = "current_font"
    
    /**
     * 初始化字體管理器
     */
    fun initialize(context: Context) {
        loadFontSettings(context)
    }

    /**
     * 設置當前字體
     */
    fun setCurrentFont(context: Context, fontType: FontType) {
        currentFontType = fontType
        saveFontSettings(context)
    }

    /**
     * 設置當前字體（向後兼容）
     */
    fun setCurrentFont(fontType: FontType) {
        currentFontType = fontType
    }
    
    /**
     * 獲取當前字體
     */
    fun getCurrentFont(): FontType {
        return currentFontType
    }
    
    /**
     * 獲取字體Typeface
     */
    fun getTypeface(context: Context, fontType: FontType = currentFontType): Typeface? {
        // 如果是系統字體，返回null（使用預設）
        if (fontType == FontType.SYSTEM) {
            return null
        }
        
        // 檢查快取
        if (fontCache.containsKey(fontType)) {
            return fontCache[fontType]
        }
        
        // 載入字體
        val typeface = try {
            fontType.fontResId?.let { resId ->
                ResourcesCompat.getFont(context, resId)
            }
        } catch (e: Exception) {
            android.util.Log.w("FontManager", "載入字體失敗: ${fontType.displayName}", e)
            null
        }
        
        // 快取字體
        fontCache[fontType] = typeface
        return typeface
    }
    
    /**
     * 獲取所有可用字體
     */
    fun getAvailableFonts(): List<FontType> {
        return FontType.values().toList()
    }
    
    /**
     * 清除字體快取
     */
    fun clearCache() {
        fontCache.clear()
    }
    
    /**
     * 檢查字體是否可用
     */
    fun isFontAvailable(context: Context, fontType: FontType): Boolean {
        if (fontType == FontType.SYSTEM) return true

        return try {
            fontType.fontResId?.let { resId ->
                ResourcesCompat.getFont(context, resId) != null
            } ?: false
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 保存字體設置
     */
    private fun saveFontSettings(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit()
            .putString(KEY_CURRENT_FONT, currentFontType.name)
            .apply()
    }

    /**
     * 載入字體設置
     */
    private fun loadFontSettings(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val fontName = prefs.getString(KEY_CURRENT_FONT, FontType.SYSTEM.name)

        currentFontType = try {
            FontType.valueOf(fontName ?: FontType.SYSTEM.name)
        } catch (e: Exception) {
            FontType.SYSTEM
        }
    }
}
