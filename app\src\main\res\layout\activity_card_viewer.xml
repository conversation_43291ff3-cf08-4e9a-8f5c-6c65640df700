<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light">

    <!-- 工具列 -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary_blue"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:title="卡片檢視"
            app:titleTextColor="@android:color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- 狀態指示區 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/background_section"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/tv_content_type"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="題目"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/primary_blue" />

        <TextView
            android:id="@+id/tv_tap_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="點擊查看答案"
            android:textSize="14sp"
            android:textColor="@color/text_secondary"
            android:background="@drawable/bg_hint_chip"
            android:paddingHorizontal="12dp"
            android:paddingVertical="6dp" />

    </LinearLayout>

    <!-- 主要內容區域 -->
    <FrameLayout
        android:id="@+id/content_frame"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_margin="16dp"
        android:background="@drawable/bg_card_content"
        android:elevation="2dp"
        android:clickable="true"
        android:focusable="true">

        <!-- 可滾動的內容容器 -->
        <ScrollView
            android:id="@+id/scroll_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:scrollbars="vertical"
            android:scrollbarStyle="insideInset"
            android:fadeScrollbars="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- 內容顯示區 -->
                <TextView
                    android:id="@+id/tv_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="16sp"
                    android:textColor="@color/text_primary"
                    android:lineSpacingExtra="4dp"
                    android:textIsSelectable="true"
                    android:minHeight="200dp"
                    android:gravity="top"
                    tools:text="這裡顯示卡片內容，可以是題目或答案。支援長文本滾動查看。" />

            </LinearLayout>

        </ScrollView>

        <!-- 載入指示器 -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />

    </FrameLayout>

    <!-- 底部操作區 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/background_section"
        android:gravity="center">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_edit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="編輯卡片"
            android:textSize="16sp"
            android:textColor="@color/primary_blue"
            style="@style/Widget.Material3.Button.OutlinedButton"
            app:strokeColor="@color/primary_blue" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_toggle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="查看答案"
            android:textSize="16sp"
            android:textColor="@android:color/white"
            android:backgroundTint="@color/primary_blue"
            style="@style/Widget.Material3.Button.OutlinedButton"
            app:strokeColor="@color/primary_blue" />

    </LinearLayout>

</LinearLayout>
