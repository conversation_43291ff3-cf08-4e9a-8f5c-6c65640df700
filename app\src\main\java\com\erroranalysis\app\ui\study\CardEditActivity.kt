package com.erroranalysis.app.ui.study

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.erroranalysis.app.databinding.ActivityCardEditBinding
import com.erroranalysis.app.ui.base.ThemedActivity
import com.erroranalysis.app.ui.theme.AppTheme
import com.erroranalysis.app.data.DeckDataManager
import com.erroranalysis.app.ui.widgets.RichTextEditText
import android.graphics.BitmapFactory
import android.provider.MediaStore
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.content.PermissionChecker
import android.Manifest
import androidx.lifecycle.lifecycleScope
import com.erroranalysis.app.utils.OCRHelper
import com.erroranalysis.app.utils.GeminiAIService
import kotlinx.coroutines.launch

/**
 * 卡片編輯Activity - 滿版編輯體驗
 */
class CardEditActivity : ThemedActivity() {
    
    private lateinit var binding: ActivityCardEditBinding
    private lateinit var dataManager: DeckDataManager
    private var editingCard: StudyCard? = null
    private lateinit var deckId: String
    private var isEditMode = false
    private var currentEditText: RichTextEditText? = null

    // OCR功能
    private lateinit var ocrHelper: OCRHelper

    // AI解答功能
    private lateinit var geminiAIService: GeminiAIService

    // 圖片選擇器
    private val imagePickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let { imageUri ->
            try {
                val inputStream = contentResolver.openInputStream(imageUri)
                val bitmap = BitmapFactory.decodeStream(inputStream)
                inputStream?.close()

                currentEditText?.insertImage(bitmap, imageUri.toString())
            } catch (e: Exception) {
                Toast.makeText(this, "圖片載入失敗：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    // 整合拍照功能
    private val cameraActivityLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            val imagePath = result.data?.getStringExtra("image_path")
            imagePath?.let { path ->
                try {
                    val bitmap = BitmapFactory.decodeFile(path)
                    bitmap?.let {
                        currentEditText?.insertImage(it)
                        // 刪除臨時文件
                        java.io.File(path).delete()
                    }
                } catch (e: Exception) {
                    Toast.makeText(this, "圖片載入失敗：${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    // 簡單相機拍照（備用）
    private val simpleCameraLauncher = registerForActivityResult(
        ActivityResultContracts.TakePicturePreview()
    ) { bitmap ->
        bitmap?.let {
            currentEditText?.insertImage(it)
        }
    }

    // 權限請求
    private val cameraPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            // 優先使用應用內的拍照功能
            launchCameraActivity()
        } else {
            Toast.makeText(this, "需要相機權限才能拍照", Toast.LENGTH_SHORT).show()
        }
    }

    // OCR圖片選擇器
    private val ocrImagePickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let { imageUri ->
            try {
                val inputStream = contentResolver.openInputStream(imageUri)
                val bitmap = BitmapFactory.decodeStream(inputStream)
                inputStream?.close()

                // 執行OCR識別
                performOCR(bitmap)
            } catch (e: Exception) {
                Toast.makeText(this, "圖片載入失敗：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }


    
    companion object {
        const val EXTRA_DECK_ID = "extra_deck_id"
        const val EXTRA_CARD = "extra_card"
        const val RESULT_CARD_SAVED = "result_card_saved"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCardEditBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // 初始化資料管理器
        dataManager = DeckDataManager(this)

        // 初始化OCR功能
        ocrHelper = OCRHelper(this)

        // 初始化AI解答功能
        geminiAIService = GeminiAIService(this)
        
        // 獲取傳入的資料
        deckId = intent.getStringExtra(EXTRA_DECK_ID) ?: run {
            finish()
            return
        }
        
        editingCard = intent.getParcelableExtra(EXTRA_CARD)
        isEditMode = editingCard != null
        
        setupToolbar()
        setupViews()
        loadCardData()

        // 應用主題
        applyTheme()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            title = if (isEditMode) "編輯卡片" else "建立卡片"
            setDisplayHomeAsUpEnabled(true)
        }
        
        // 設置按鈕點擊事件
        binding.buttonCancel.setOnClickListener {
            finish()
        }
        
        binding.buttonSave.setOnClickListener {
            saveCard()
        }
        
        // 設置圖片按鈕點擊事件
        binding.buttonAddQuestionImage.setOnClickListener {
            currentEditText = binding.editQuestion
            showImagePickerDialog()
        }

        binding.buttonAddAnswerImage.setOnClickListener {
            currentEditText = binding.editAnswer
            showImagePickerDialog()
        }

        // 設置圖片旋轉按鈕點擊事件
        binding.buttonRotateQuestionImage.setOnClickListener {
            rotateQuestionImages()
        }

        // 設置OCR按鈕點擊事件
        binding.buttonOcrQuestion.setOnClickListener {
            currentEditText = binding.editQuestion
            showOCRDialog()
        }

        binding.buttonOcrAnswer.setOnClickListener {
            currentEditText = binding.editAnswer
            showOCRDialog()
        }

        // 設置AI解答按鈕點擊事件
        binding.buttonAiAnswer.setOnClickListener {
            generateAIAnswer()
        }

        // 長按AI按鈕進行API測試
        binding.buttonAiAnswer.setOnLongClickListener {
            testGeminiAPI()
            true
        }
    }
    
    private fun setupViews() {
        // RichTextEditText已經內建滑動功能，不需要額外設置
    }

    private fun showImagePickerDialog() {
        val options = arrayOf("從相簿選擇", "專業拍照", "簡單拍照", "取消")

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("選擇圖片來源")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> {
                        // 從相簿選擇
                        imagePickerLauncher.launch("image/*")
                    }
                    1 -> {
                        // 專業拍照 - 使用應用內的拍照功能
                        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                            == PermissionChecker.PERMISSION_GRANTED) {
                            launchCameraActivity()
                        } else {
                            cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
                        }
                    }
                    2 -> {
                        // 簡單拍照 - 使用系統相機
                        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                            == PermissionChecker.PERMISSION_GRANTED) {
                            simpleCameraLauncher.launch(null)
                        } else {
                            cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
                        }
                    }
                    3 -> {
                        // 取消
                    }
                }
            }
            .show()
    }

    private fun showOCRDialog() {
        val options = arrayOf("從相簿選擇圖片識別", "從既有圖片識別", "取消")

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("OCR文字識別")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> {
                        // 從相簿選擇圖片進行OCR
                        ocrImagePickerLauncher.launch("image/*")
                    }
                    1 -> {
                        // 從既有圖片進行OCR
                        showExistingImageOCRDialog()
                    }
                    2 -> {
                        // 取消
                    }
                }
            }
            .show()
    }

    private fun launchCameraActivity() {
        try {
            val intent = Intent(this, com.erroranalysis.app.ui.camera.CameraActivity::class.java)
            intent.putExtra("return_image", true)
            cameraActivityLauncher.launch(intent)
        } catch (e: Exception) {
            Toast.makeText(this, "無法啟動拍照功能，使用簡單拍照", Toast.LENGTH_SHORT).show()
            simpleCameraLauncher.launch(null)
        }
    }





    /**
     * 旋轉題目中的圖片90度
     */
    private fun rotateQuestionImages() {
        val images = binding.editQuestion.getAllImages()

        if (images.isEmpty()) {
            Toast.makeText(this, "題目中沒有圖片可以旋轉", Toast.LENGTH_SHORT).show()
            return
        }

        // 顯示確認對話框
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("旋轉圖片")
            .setMessage("將題目中的所有圖片逆時針旋轉90度，此操作無法撤銷。確定要繼續嗎？")
            .setPositiveButton("確定") { _, _ ->
                try {
                    // 執行旋轉
                    binding.editQuestion.rotateAllImages90Degrees()
                    Toast.makeText(this, "已成功旋轉 ${images.size} 張圖片", Toast.LENGTH_SHORT).show()
                } catch (e: Exception) {
                    Toast.makeText(this, "旋轉圖片失敗：${e.message}", Toast.LENGTH_LONG).show()
                    android.util.Log.e("CardEditActivity", "旋轉圖片失敗", e)
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 顯示既有圖片OCR選擇對話框
     */
    private fun showExistingImageOCRDialog() {
        // 獲取當前編輯框中的所有圖片
        val images = currentEditText?.getAllImages() ?: emptyList()

        if (images.isEmpty()) {
            Toast.makeText(this, "當前編輯框中沒有圖片", Toast.LENGTH_SHORT).show()
            return
        }

        // 創建選項列表
        val options = images.map { it.description }.toTypedArray() + "取消"

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("選擇要識別的圖片")
            .setItems(options) { _, which ->
                if (which < images.size) {
                    // 選擇了某個圖片
                    val selectedImage = images[which]
                    performOCR(selectedImage.bitmap)
                }
                // 最後一個選項是"取消"，不需要處理
            }
            .show()
    }

    /**
     * 執行OCR文字識別
     */
    private fun performOCR(bitmap: android.graphics.Bitmap) {
        // 在後台線程執行OCR
        Thread {
            try {
                // 在主線程顯示進度提示
                runOnUiThread {
                    val progressDialog = androidx.appcompat.app.AlertDialog.Builder(this@CardEditActivity)
                        .setTitle("OCR識別中")
                        .setMessage("正在識別圖片中的文字，請稍候...")
                        .setCancelable(false)
                        .create()
                    progressDialog.show()

                    // 在後台執行OCR
                    Thread {
                        try {
                            // 預處理圖片
                            val processedBitmap = ocrHelper.preprocessImage(bitmap)

                            // 執行OCR識別
                            val recognizedText = ocrHelper.recognizeText(processedBitmap)

                            // 後處理文字
                            val finalText = ocrHelper.postprocessText(recognizedText)

                            // 回到主線程顯示結果
                            runOnUiThread {
                                progressDialog.dismiss()
                                showOCRResult(finalText)
                            }

                        } catch (e: Exception) {
                            runOnUiThread {
                                progressDialog.dismiss()
                                Toast.makeText(this@CardEditActivity, "OCR識別失敗：${e.message}", Toast.LENGTH_LONG).show()
                                android.util.Log.e("CardEditActivity", "OCR識別失敗", e)
                            }
                        }
                    }.start()
                }

            } catch (e: Exception) {
                runOnUiThread {
                    Toast.makeText(this@CardEditActivity, "OCR識別失敗：${e.message}", Toast.LENGTH_LONG).show()
                    android.util.Log.e("CardEditActivity", "OCR識別失敗", e)
                }
            }
        }.start()
    }

    /**
     * 顯示OCR識別結果
     */
    private fun showOCRResult(recognizedText: String) {
        if (recognizedText.isEmpty()) {
            Toast.makeText(this, "未識別到文字內容", Toast.LENGTH_SHORT).show()
            return
        }

        // 創建可編輯的對話框
        val editText = android.widget.EditText(this).apply {
            setText(recognizedText)
            setSelection(recognizedText.length) // 將光標移到末尾
            maxLines = 10
            inputType = android.text.InputType.TYPE_CLASS_TEXT or android.text.InputType.TYPE_TEXT_FLAG_MULTI_LINE
        }

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("OCR識別結果")
            .setMessage("請確認識別結果，可以編輯修正：")
            .setView(editText)
            .setPositiveButton("插入文字") { _, _ ->
                val finalText = editText.text.toString()
                if (finalText.isNotEmpty()) {
                    // 將識別的文字插入到當前編輯框
                    currentEditText?.let { editTextView ->
                        val currentText = editTextView.text.toString()
                        val cursorPosition = editTextView.selectionStart
                        val newText = StringBuilder(currentText)
                            .insert(cursorPosition, finalText)
                            .toString()
                        editTextView.setText(newText)
                        editTextView.setSelection(cursorPosition + finalText.length)
                    }
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun loadCardData() {
        editingCard?.let { card ->
            // 如果卡片內容是JSON格式（包含圖片），使用setRichContent
            // 否則當作純文字處理
            try {
                binding.editQuestion.setRichContent(card.question)
            } catch (e: Exception) {
                binding.editQuestion.setText(card.question)
            }

            try {
                binding.editAnswer.setRichContent(card.answer)
            } catch (e: Exception) {
                binding.editAnswer.setText(card.answer)
            }

            // 載入AI解答
            binding.editAiAnswer.setText(card.aiAnswer)

            binding.editTags.setText(card.tags.joinToString(", "))
        }
    }
    
    private fun saveCard() {
        // 獲取圖文混合內容
        val question = binding.editQuestion.getRichContent()
        val answer = binding.editAnswer.getRichContent()
        val aiAnswer = binding.editAiAnswer.text.toString().trim()
        val tagsString = binding.editTags.text.toString().trim()

        // 驗證輸入（只檢查題目是否有內容，答案可以為空）
        val hasQuestionContent = binding.editQuestion.hasContent()

        if (!hasQuestionContent) {
            binding.editQuestion.error = "請輸入題目或插入圖片"
            binding.editQuestion.requestFocus()
            return
        }
        
        // 處理標籤
        val tags = if (tagsString.isNotEmpty()) {
            tagsString.split(",").map { it.trim() }.filter { it.isNotEmpty() }
        } else {
            emptyList()
        }
        
        try {
            if (isEditMode) {
                // 更新現有卡片
                val updatedCard = editingCard!!.copy(
                    question = question,
                    answer = answer,
                    aiAnswer = aiAnswer,
                    tags = tags
                )
                dataManager.updateCard(updatedCard)
                Toast.makeText(this, "卡片已更新", Toast.LENGTH_SHORT).show()
            } else {
                // 建立新卡片
                val newCard = StudyCard(
                    id = dataManager.generateNewCardId(),
                    deckId = deckId,
                    question = question,
                    answer = answer,
                    aiAnswer = aiAnswer,
                    tags = tags
                )
                dataManager.addCard(newCard)
                Toast.makeText(this, "卡片已建立", Toast.LENGTH_SHORT).show()
            }
            
            // 設置結果並關閉Activity
            setResult(RESULT_OK, Intent().putExtra(RESULT_CARD_SAVED, true))
            finish()
            
        } catch (e: Exception) {
            Toast.makeText(this, "保存失敗：${e.message}", Toast.LENGTH_LONG).show()
        }
    }
    
    override fun onSupportNavigateUp(): Boolean {
        finish()
        return true
    }
    
    override fun onBackPressed() {
        // 檢查是否有未保存的變更
        val hasChanges = if (isEditMode) {
            val currentQuestion = binding.editQuestion.getRichContent()
            val currentAnswer = binding.editAnswer.getRichContent()
            val currentTags = binding.editTags.text.toString().trim()

            editingCard?.let { card ->
                currentQuestion != card.question ||
                currentAnswer != card.answer ||
                currentTags != card.tags.joinToString(", ")
            } ?: false
        } else {
            binding.editQuestion.hasContent() ||
            binding.editAnswer.hasContent() ||
            binding.editTags.text.toString().trim().isNotEmpty()
        }
        
        if (hasChanges) {
            androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("放棄變更")
                .setMessage("您有未保存的變更，確定要離開嗎？")
                .setPositiveButton("離開") { _, _ -> super.onBackPressed() }
                .setNegativeButton("繼續編輯", null)
                .show()
        } else {
            super.onBackPressed()
        }
    }

    /**
     * 測試Gemini API連接
     */
    private fun testGeminiAPI() {
        Toast.makeText(this, "正在測試API連接...", Toast.LENGTH_SHORT).show()

        lifecycleScope.launch {
            try {
                val testResult = geminiAIService.testApiConnection()

                runOnUiThread {
                    androidx.appcompat.app.AlertDialog.Builder(this@CardEditActivity)
                        .setTitle("API測試結果")
                        .setMessage(testResult)
                        .setPositiveButton("確定", null)
                        .show()
                }

            } catch (e: Exception) {
                runOnUiThread {
                    androidx.appcompat.app.AlertDialog.Builder(this@CardEditActivity)
                        .setTitle("API測試失敗")
                        .setMessage("錯誤：${e.message}")
                        .setPositiveButton("確定", null)
                        .show()
                }
            }
        }
    }

    /**
     * 生成AI解答
     */
    private fun generateAIAnswer() {
        // 檢查API是否可用
        if (!geminiAIService.isApiAvailable()) {
            Toast.makeText(this, "AI服務暫時不可用", Toast.LENGTH_SHORT).show()
            return
        }

        // 獲取題目內容
        val questionText = binding.editQuestion.text.toString().trim()
        val questionImages = binding.editQuestion.getAllImages()

        if (questionText.isEmpty() && questionImages.isEmpty()) {
            Toast.makeText(this, "請先輸入題目內容或添加題目圖片", Toast.LENGTH_SHORT).show()
            return
        }

        // 顯示處理中狀態
        showAIProcessingState(true)

        // 立即顯示處理中的提示
        binding.editAiAnswer.setText("🤖 AI正在分析題目並生成解答...\n\n請稍候，這可能需要幾秒鐘時間。")

        // 顯示確認Toast
        Toast.makeText(this, "題目已送出，AI正在處理中...", Toast.LENGTH_LONG).show()

        // 在後台調用AI API
        lifecycleScope.launch {
            try {
                // 提取圖片的Bitmap
                val bitmaps = questionImages.map { it.bitmap }

                // 調用Gemini API
                val aiResponse = geminiAIService.solveQuestion(questionText, bitmaps)

                // 在主線程更新UI
                runOnUiThread {
                    // 恢復正常狀態
                    showAIProcessingState(false)

                    // 顯示AI解答
                    binding.editAiAnswer.setText(aiResponse)

                    // 提取AI解答的最後一行並放入答案欄
                    extractAndSetFinalAnswer(aiResponse)

                    Toast.makeText(this@CardEditActivity, "✅ AI解答完成！", Toast.LENGTH_SHORT).show()
                }

            } catch (e: Exception) {
                runOnUiThread {
                    // 恢復正常狀態
                    showAIProcessingState(false)

                    binding.editAiAnswer.setText("❌ AI解答失敗：${e.message}\n\n請檢查網路連接或稍後再試。")
                    Toast.makeText(this@CardEditActivity, "❌ AI解答失敗", Toast.LENGTH_SHORT).show()
                }
                android.util.Log.e("CardEditActivity", "AI解答失敗", e)
            }
        }
    }

    /**
     * 顯示AI處理狀態
     */
    private fun showAIProcessingState(isProcessing: Boolean) {
        if (isProcessing) {
            // 禁用AI按鈕，防止重複點擊
            binding.buttonAiAnswer.isEnabled = false
            binding.buttonAiAnswer.alpha = 0.6f
            binding.buttonAiAnswer.text = "🤖 AI思考中..."

        } else {
            // 恢復AI按鈕
            binding.buttonAiAnswer.isEnabled = true
            binding.buttonAiAnswer.alpha = 1.0f
            binding.buttonAiAnswer.text = "🤖 AI解答"
        }
    }

    /**
     * 提取AI解答的最後一行並設置到答案欄
     */
    private fun extractAndSetFinalAnswer(aiResponse: String) {
        try {
            // 分割AI回應為行
            val lines = aiResponse.split("\n").map { it.trim() }.filter { it.isNotEmpty() }

            if (lines.isNotEmpty()) {
                // 取最後一行作為最終答案
                val finalAnswer = lines.last()

                // 檢查最後一行是否看起來像答案
                if (isFinalAnswerLine(finalAnswer)) {
                    // 如果答案欄為空，則設置AI的最終答案
                    if (!binding.editAnswer.hasContent()) {
                        binding.editAnswer.setText(finalAnswer)
                        android.util.Log.d("CardEditActivity", "已將AI最終答案設置到答案欄: $finalAnswer")
                    }
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("CardEditActivity", "提取最終答案失敗", e)
        }
    }

    /**
     * 判斷是否為最終答案行
     */
    private fun isFinalAnswerLine(line: String): Boolean {
        // 檢查是否包含答案關鍵詞
        val answerKeywords = listOf(
            "答案", "答：", "解：", "結果", "所以", "因此", "最終答案",
            "答案是", "等於", "=", "Answer:", "Result:", "Therefore"
        )

        // 檢查是否包含數字或數學表達式
        val hasNumber = line.any { it.isDigit() }
        val hasMathSymbol = line.any { it in "+-×÷=()[]{}√π∞" }

        // 如果包含答案關鍵詞，或者包含數字/數學符號且長度適中，則認為是答案
        return answerKeywords.any { keyword ->
            line.contains(keyword, ignoreCase = true)
        } || (hasNumber && line.length < 100) || hasMathSymbol
    }

    override fun onDestroy() {
        super.onDestroy()
        // 釋放OCR資源
        if (::ocrHelper.isInitialized) {
            ocrHelper.release()
        }
    }

    override fun onApplyTheme(theme: AppTheme) {
        // 應用主題到根佈局背景
        findViewById<android.view.View>(android.R.id.content).setBackgroundColor(theme.getBackgroundColorInt())

        // 應用主題到工具欄
        binding.toolbar.setBackgroundColor(theme.getPrimaryColorInt())
    }
}
