// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogFontSelectorBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton btnApply;

  @NonNull
  public final MaterialButton btnCancel;

  @NonNull
  public final RecyclerView recyclerFonts;

  @NonNull
  public final TextView textFontPreview;

  private DialogFontSelectorBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton btnApply, @NonNull MaterialButton btnCancel,
      @NonNull RecyclerView recyclerFonts, @NonNull TextView textFontPreview) {
    this.rootView = rootView;
    this.btnApply = btnApply;
    this.btnCancel = btnCancel;
    this.recyclerFonts = recyclerFonts;
    this.textFontPreview = textFontPreview;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogFontSelectorBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogFontSelectorBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_font_selector, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogFontSelectorBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_apply;
      MaterialButton btnApply = ViewBindings.findChildViewById(rootView, id);
      if (btnApply == null) {
        break missingId;
      }

      id = R.id.btn_cancel;
      MaterialButton btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.recycler_fonts;
      RecyclerView recyclerFonts = ViewBindings.findChildViewById(rootView, id);
      if (recyclerFonts == null) {
        break missingId;
      }

      id = R.id.text_font_preview;
      TextView textFontPreview = ViewBindings.findChildViewById(rootView, id);
      if (textFontPreview == null) {
        break missingId;
      }

      return new DialogFontSelectorBinding((LinearLayout) rootView, btnApply, btnCancel,
          recyclerFonts, textFontPreview);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
