<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <!-- 狀態指示器 -->
        <View
            android:id="@+id/indicator_status"
            android:layout_width="4dp"
            android:layout_height="match_parent"
            android:background="@color/primary" />

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="16dp">

            <!-- 星星圖標 -->
            <ImageView
                android:id="@+id/icon_star"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_alignParentTop="true"
                android:layout_alignParentEnd="true"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:padding="2dp"
                android:src="@drawable/ic_star_outline"
                android:contentDescription="標記為重要" />

            <!-- 主要內容區域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="32dp"
                android:orientation="vertical">

            <!-- 題目 -->
            <TextView
                android:id="@+id/text_question"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="3"
                android:text="這是一個數學題目"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- 預覽提示 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="點擊查看完整內容"
                android:textColor="@color/text_tertiary"
                android:textSize="12sp"
                android:textStyle="italic" />

            <!-- 答案 - 預設隱藏 -->
            <TextView
                android:id="@+id/text_answer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="這是答案內容"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:visibility="gone" />

            <!-- 標籤 - 預設隱藏 -->
            <TextView
                android:id="@+id/text_tags"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="代數 • 方程式"
                android:textColor="@color/text_tertiary"
                android:textSize="12sp"
                android:visibility="gone" />

            <!-- 底部資訊區域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <!-- 熟練度 -->
                <TextView
                    android:id="@+id/text_difficulty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/chip_background"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="4dp"
                    android:text="普通"
                    android:textSize="10sp" />

                <!-- 掌握程度 -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ProgressBar
                        android:id="@+id/progress_mastery"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:layout_width="0dp"
                        android:layout_height="6dp"
                        android:layout_weight="1"
                        android:max="100"
                        android:progress="60" />

                    <TextView
                        android:id="@+id/text_mastery_level"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:text="熟悉"
                        android:textColor="@color/text_tertiary"
                        android:textSize="10sp" />

                </LinearLayout>

                <!-- 建立時間 -->
                <TextView
                    android:id="@+id/text_created_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:text="01/15"
                    android:textColor="@color/text_tertiary"
                    android:textSize="10sp" />

            </LinearLayout>

            <!-- 統計資訊 -->
            <TextView
                android:id="@+id/text_stats"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="複習 5 次 • 正確率 80%"
                android:textColor="@color/text_tertiary"
                android:textSize="10sp"
                android:visibility="gone" />

            </LinearLayout>

        </RelativeLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
