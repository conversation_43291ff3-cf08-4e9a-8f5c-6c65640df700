<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_study_card" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\item_study_card.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_study_card_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="172" endOffset="51"/></Target><Target id="@+id/indicator_status" view="View"><Expressions/><location startLine="17" startOffset="8" endLine="21" endOffset="49"/></Target><Target id="@+id/icon_star" view="ImageView"><Expressions/><location startLine="30" startOffset="12" endLine="41" endOffset="52"/></Target><Target id="@+id/text_question" view="TextView"><Expressions/><location startLine="51" startOffset="12" endLine="60" endOffset="42"/></Target><Target id="@+id/text_answer" view="TextView"><Expressions/><location startLine="73" startOffset="12" endLine="83" endOffset="43"/></Target><Target id="@+id/text_tags" view="TextView"><Expressions/><location startLine="86" startOffset="12" endLine="94" endOffset="43"/></Target><Target id="@+id/text_difficulty" view="TextView"><Expressions/><location startLine="104" startOffset="16" endLine="112" endOffset="45"/></Target><Target id="@+id/progress_mastery" view="ProgressBar"><Expressions/><location startLine="123" startOffset="20" endLine="130" endOffset="47"/></Target><Target id="@+id/text_mastery_level" view="TextView"><Expressions/><location startLine="132" startOffset="20" endLine="139" endOffset="49"/></Target><Target id="@+id/text_created_time" view="TextView"><Expressions/><location startLine="144" startOffset="16" endLine="151" endOffset="45"/></Target><Target id="@+id/text_stats" view="TextView"><Expressions/><location startLine="156" startOffset="12" endLine="164" endOffset="43"/></Target></Targets></Layout>