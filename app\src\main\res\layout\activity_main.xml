<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ui.main.MainActivity">

    <!-- 頂部標題區域 -->
    <LinearLayout
        android:id="@+id/layout_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/gradient_primary"
        android:orientation="vertical"
        android:padding="24dp"
        android:paddingTop="48dp"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/main_title"
            android:textColor="@color/text_white"
            android:textSize="28sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="@string/main_subtitle"
            android:textColor="@color/text_white"
            android:textSize="14sp"
            android:alpha="0.9" />

    </LinearLayout>

    <!-- 主要內容區域 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toTopOf="@id/bottom_navigation"
        app:layout_constraintTop_toBottomOf="@id/layout_header">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- 主功能按鈕區域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/btn_camera"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_marginEnd="8dp"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/gradient_primary"
                            android:gravity="center"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:src="@drawable/ic_camera"
                                android:tint="@color/text_white" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/menu_camera"
                                android:textColor="@color/text_white"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/btn_error_bank"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_marginStart="8dp"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/gradient_primary"
                            android:gravity="center"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:src="@drawable/ic_folder"
                                android:tint="@color/text_white" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/menu_error_bank"
                                android:textColor="@color/text_white"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/btn_report"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_marginEnd="8dp"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/gradient_primary"
                            android:gravity="center"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:src="@drawable/ic_chart"
                                android:tint="@color/text_white" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/menu_report"
                                android:textColor="@color/text_white"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/btn_practice"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_marginStart="8dp"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/gradient_primary"
                            android:gravity="center"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:src="@drawable/ic_lightbulb"
                                android:tint="@color/text_white" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/menu_practice"
                                android:textColor="@color/text_white"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

            </LinearLayout>

            <!-- 今日學習統計 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/today_learning"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:drawableStart="@drawable/ic_clock"
                        android:drawablePadding="8dp"
                        android:drawableTint="@color/primary_blue" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal"
                        android:weightSum="2">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_analyzed_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="5"
                                android:textColor="@color/primary_blue"
                                android:textSize="32sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/analyzed_questions"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_error_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="3"
                                android:textColor="@color/primary_blue"
                                android:textSize="32sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/error_count"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </ScrollView>

    <!-- 底部導航 -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background_white"
        app:itemIconTint="@color/bottom_nav_color"
        app:itemTextColor="@color/bottom_nav_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:menu="@menu/bottom_navigation" />

</androidx.constraintlayout.widget.ConstraintLayout>
