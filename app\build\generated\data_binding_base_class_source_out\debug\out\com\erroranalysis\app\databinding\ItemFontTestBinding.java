// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemFontTestBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextView textFontName;

  @NonNull
  public final TextView textSample;

  private ItemFontTestBinding(@NonNull CardView rootView, @NonNull TextView textFontName,
      @NonNull TextView textSample) {
    this.rootView = rootView;
    this.textFontName = textFontName;
    this.textSample = textSample;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemFontTestBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemFontTestBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_font_test, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemFontTestBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.text_font_name;
      TextView textFontName = ViewBindings.findChildViewById(rootView, id);
      if (textFontName == null) {
        break missingId;
      }

      id = R.id.text_sample;
      TextView textSample = ViewBindings.findChildViewById(rootView, id);
      if (textSample == null) {
        break missingId;
      }

      return new ItemFontTestBinding((CardView) rootView, textFontName, textSample);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
