// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCardViewerBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton btnEdit;

  @NonNull
  public final MaterialButton btnToggle;

  @NonNull
  public final ImageButton btnTts;

  @NonNull
  public final FrameLayout contentFrame;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final ScrollView scrollView;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvContent;

  @NonNull
  public final TextView tvContentType;

  @NonNull
  public final TextView tvTapHint;

  private ActivityCardViewerBinding(@NonNull LinearLayout rootView, @NonNull MaterialButton btnEdit,
      @NonNull MaterialButton btnToggle, @NonNull ImageButton btnTts,
      @NonNull FrameLayout contentFrame, @NonNull ProgressBar progressBar,
      @NonNull ScrollView scrollView, @NonNull Toolbar toolbar, @NonNull TextView tvContent,
      @NonNull TextView tvContentType, @NonNull TextView tvTapHint) {
    this.rootView = rootView;
    this.btnEdit = btnEdit;
    this.btnToggle = btnToggle;
    this.btnTts = btnTts;
    this.contentFrame = contentFrame;
    this.progressBar = progressBar;
    this.scrollView = scrollView;
    this.toolbar = toolbar;
    this.tvContent = tvContent;
    this.tvContentType = tvContentType;
    this.tvTapHint = tvTapHint;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCardViewerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCardViewerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_card_viewer, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCardViewerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_edit;
      MaterialButton btnEdit = ViewBindings.findChildViewById(rootView, id);
      if (btnEdit == null) {
        break missingId;
      }

      id = R.id.btn_toggle;
      MaterialButton btnToggle = ViewBindings.findChildViewById(rootView, id);
      if (btnToggle == null) {
        break missingId;
      }

      id = R.id.btn_tts;
      ImageButton btnTts = ViewBindings.findChildViewById(rootView, id);
      if (btnTts == null) {
        break missingId;
      }

      id = R.id.content_frame;
      FrameLayout contentFrame = ViewBindings.findChildViewById(rootView, id);
      if (contentFrame == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.scroll_view;
      ScrollView scrollView = ViewBindings.findChildViewById(rootView, id);
      if (scrollView == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_content;
      TextView tvContent = ViewBindings.findChildViewById(rootView, id);
      if (tvContent == null) {
        break missingId;
      }

      id = R.id.tv_content_type;
      TextView tvContentType = ViewBindings.findChildViewById(rootView, id);
      if (tvContentType == null) {
        break missingId;
      }

      id = R.id.tv_tap_hint;
      TextView tvTapHint = ViewBindings.findChildViewById(rootView, id);
      if (tvTapHint == null) {
        break missingId;
      }

      return new ActivityCardViewerBinding((LinearLayout) rootView, btnEdit, btnToggle, btnTts,
          contentFrame, progressBar, scrollView, toolbar, tvContent, tvContentType, tvTapHint);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
