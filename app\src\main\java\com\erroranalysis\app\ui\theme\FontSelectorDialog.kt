package com.erroranalysis.app.ui.theme

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.erroranalysis.app.databinding.DialogFontSelectorBinding

/**
 * 字體選擇對話框
 */
class FontSelectorDialog(
    context: Context,
    private val currentFont: FontManager.FontType,
    private val onFontSelected: (FontManager.FontType) -> Unit
) : Dialog(context) {

    private lateinit var binding: DialogFontSelectorBinding
    private lateinit var fontAdapter: FontSelectorAdapter
    private var selectedFont = currentFont

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = DialogFontSelectorBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 輸出字體診斷信息
        val diagnosis = FontManager.diagnoseFontIssues(context)
        android.util.Log.d("FontDialog", diagnosis)

        setupDialog()
        setupRecyclerView()
        setupButtons()
        updatePreview()
    }
    
    private fun setupDialog() {
        // 設置對話框屬性
        window?.setLayout(
            (context.resources.displayMetrics.widthPixels * 0.9).toInt(),
            android.view.ViewGroup.LayoutParams.WRAP_CONTENT
        )
    }
    
    private fun setupRecyclerView() {
        val availableFonts = FontManager.getAvailableFonts(context)
        android.util.Log.d("FontDialog", "可用字體數量: ${availableFonts.size}")

        fontAdapter = FontSelectorAdapter(
            fonts = availableFonts,
            selectedFont = selectedFont
        ) { fontType ->
            selectedFont = fontType
            updatePreview()
        }

        binding.recyclerFonts.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = fontAdapter
        }
    }
    
    private fun setupButtons() {
        binding.btnCancel.setOnClickListener {
            dismiss()
        }
        
        binding.btnApply.setOnClickListener {
            onFontSelected(selectedFont)
            dismiss()
        }
    }
    
    private fun updatePreview() {
        try {
            // 設置預覽文字內容（確保總是有文字）
            val previewText = "字體預覽：${selectedFont.displayName}\n" +
                    "這是一段示例文字，用於預覽字體效果。\n" +
                    "The quick brown fox jumps over the lazy dog.\n" +
                    "1234567890 !@#$%^&*()\n" +
                    "床前明月光，疑是地上霜。"

            binding.textFontPreview.text = previewText
            android.util.Log.d("FontDialog", "設置預覽文字: ${previewText.length} 字符")

            // 強制刷新TextView
            binding.textFontPreview.invalidate()

            // 更新預覽文字的字體
            val typeface = FontManager.getTypeface(context, selectedFont)
            android.util.Log.d("FontDialog", "更新預覽字體: ${selectedFont.displayName}, typeface: $typeface")

            if (typeface != null) {
                binding.textFontPreview.typeface = typeface
                android.util.Log.d("FontDialog", "預覽字體設置成功: ${selectedFont.displayName}")

                // 強制重繪
                binding.textFontPreview.requestLayout()
                binding.textFontPreview.invalidate()
            } else {
                binding.textFontPreview.typeface = null
                android.util.Log.d("FontDialog", "使用系統預覽字體: ${selectedFont.displayName}")
            }

            // 確保TextView可見
            binding.textFontPreview.visibility = android.view.View.VISIBLE

        } catch (e: Exception) {
            android.util.Log.e("FontDialog", "更新預覽失敗: ${selectedFont.displayName}", e)

            // 錯誤時設置默認文字
            binding.textFontPreview.text = "字體預覽載入失敗：${selectedFont.displayName}"
            binding.textFontPreview.typeface = null
        }
    }
}
