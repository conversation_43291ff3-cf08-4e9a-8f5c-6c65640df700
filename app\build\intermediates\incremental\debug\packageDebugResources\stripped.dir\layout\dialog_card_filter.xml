<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- 標題 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="篩選卡片"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary" />

    <!-- 關鍵字搜尋 -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="搜尋關鍵字"
        app:startIconDrawable="@drawable/ic_search">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edit_keyword"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1"
            android:textColor="@color/text_primary"
            android:textColorHint="@color/text_secondary" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 熟練度篩選 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="熟練度"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary" />

    <com.google.android.material.chip.ChipGroup
        android:id="@+id/chip_group_mastery"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:selectionRequired="false"
        app:singleSelection="false">

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_not_learned"
            style="@style/Widget.MaterialComponents.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="未學習"
            android:textColor="@color/text_primary" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_beginner"
            style="@style/Widget.MaterialComponents.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="初學"
            android:textColor="@color/text_primary" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_learning"
            style="@style/Widget.MaterialComponents.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="學習中"
            android:textColor="@color/text_primary" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_familiar"
            style="@style/Widget.MaterialComponents.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="熟悉"
            android:textColor="@color/text_primary" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_proficient"
            style="@style/Widget.MaterialComponents.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="熟練"
            android:textColor="@color/text_primary" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_mastered"
            style="@style/Widget.MaterialComponents.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="精通"
            android:textColor="@color/text_primary" />

    </com.google.android.material.chip.ChipGroup>

    <!-- 重要標記篩選 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="重要標記"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary" />

    <com.google.android.material.chip.ChipGroup
        android:id="@+id/chip_group_star"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:selectionRequired="false"
        app:singleSelection="false">

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_starred"
            style="@style/Widget.MaterialComponents.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="⭐ 重要卡片"
            android:textColor="@color/text_primary" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_not_starred"
            style="@style/Widget.MaterialComponents.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="普通卡片"
            android:textColor="@color/text_primary" />

    </com.google.android.material.chip.ChipGroup>

    <!-- 標籤篩選 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="標籤"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary" />

    <com.google.android.material.chip.ChipGroup
        android:id="@+id/chip_group_tags"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:selectionRequired="false"
        app:singleSelection="false" />

    <!-- 按鈕區域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_clear"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:text="清除"
            android:textColor="@color/text_secondary" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_apply"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="套用"
            android:backgroundTint="@color/primary_blue" />

    </LinearLayout>

</LinearLayout>
