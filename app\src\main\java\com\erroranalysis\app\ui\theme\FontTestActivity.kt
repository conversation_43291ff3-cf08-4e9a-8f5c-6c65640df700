package com.erroranalysis.app.ui.theme

import android.os.Bundle
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.erroranalysis.app.R
import com.erroranalysis.app.databinding.ActivityFontTestBinding

/**
 * 字體測試Activity
 * 用於測試和預覽所有可用字體
 */
class FontTestActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityFontTestBinding
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFontTestBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupToolbar()
        setupFontList()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "字體測試"
    }
    
    private fun setupFontList() {
        val fonts = FontManager.getAvailableFonts()
        val fontAdapter = FontTestAdapter(fonts)

        binding.recyclerFonts.apply {
            layoutManager = LinearLayoutManager(this@FontTestActivity)
            adapter = fontAdapter
        }
    }
    
    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}

/**
 * 字體測試適配器
 */
class FontTestAdapter(private val fonts: List<FontManager.FontType>) : 
    RecyclerView.Adapter<FontTestAdapter.FontTestViewHolder>() {
    
    private val testText = """
        字體測試 Font Test
        
        中文測試：這是一段中文測試文字，包含常用漢字。
        English Test: The quick brown fox jumps over the lazy dog.
        數字測試：1234567890
        符號測試：!@#$%^&*()_+-=[]{}|;:,.<>?
        
        詩詞測試：
        床前明月光，疑是地上霜。
        舉頭望明月，低頭思故鄉。
    """.trimIndent()
    
    inner class FontTestViewHolder(itemView: android.view.View) : RecyclerView.ViewHolder(itemView) {
        private val textFontName: TextView = itemView.findViewById(R.id.text_font_name)
        private val textSample: TextView = itemView.findViewById(R.id.text_sample)
        
        fun bind(fontType: FontManager.FontType) {
            textFontName.text = fontType.displayName
            textSample.text = testText
            
            // 應用字體
            val typeface = FontManager.getTypeface(itemView.context, fontType)
            if (typeface != null) {
                textSample.typeface = typeface
            } else {
                textSample.typeface = null
            }
        }
    }
    
    override fun onCreateViewHolder(parent: android.view.ViewGroup, viewType: Int): FontTestViewHolder {
        val view = android.view.LayoutInflater.from(parent.context)
            .inflate(R.layout.item_font_test, parent, false)
        return FontTestViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: FontTestViewHolder, position: Int) {
        holder.bind(fonts[position])
    }
    
    override fun getItemCount(): Int = fonts.size
}
