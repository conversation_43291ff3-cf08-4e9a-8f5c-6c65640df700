<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- 標題 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="篩選卡組"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary" />

    <!-- 關鍵字搜尋 -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="搜尋關鍵字"
        app:startIconDrawable="@drawable/ic_search">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edit_keyword"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1"
            android:textColor="@color/text_primary"
            android:textColorHint="@color/text_secondary" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 星星篩選 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="重要標記"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary" />

    <com.google.android.material.chip.ChipGroup
        android:id="@+id/chip_group_star"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:selectionRequired="false"
        app:singleSelection="false">

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_starred"
            style="@style/Widget.MaterialComponents.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="⭐ 重要卡組"
            android:textColor="@color/text_primary" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_not_starred"
            style="@style/Widget.MaterialComponents.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="普通卡組"
            android:textColor="@color/text_primary" />

    </com.google.android.material.chip.ChipGroup>

    <!-- 卡片數量篩選 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="卡片數量"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary" />

    <com.google.android.material.chip.ChipGroup
        android:id="@+id/chip_group_card_count"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:selectionRequired="false"
        app:singleSelection="false">

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_empty"
            style="@style/Widget.MaterialComponents.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="空卡組 (0張)"
            android:textColor="@color/text_primary" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_few_cards"
            style="@style/Widget.MaterialComponents.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="少量 (1-10張)"
            android:textColor="@color/text_primary" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_many_cards"
            style="@style/Widget.MaterialComponents.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="大量 (10張以上)"
            android:textColor="@color/text_primary" />

    </com.google.android.material.chip.ChipGroup>

    <!-- 按鈕區域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_clear"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:text="清除"
            android:textColor="@color/text_secondary" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_apply"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="套用"
            android:backgroundTint="@color/primary_blue" />

    </LinearLayout>

</LinearLayout>
