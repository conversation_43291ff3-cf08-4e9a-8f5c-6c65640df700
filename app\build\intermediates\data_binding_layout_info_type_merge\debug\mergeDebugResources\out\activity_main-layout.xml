<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="329" endOffset="51"/></Target><Target id="@+id/layout_header" view="LinearLayout"><Expressions/><location startLine="10" startOffset="4" endLine="37" endOffset="18"/></Target><Target id="@+id/btn_camera" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="65" startOffset="20" endLine="100" endOffset="71"/></Target><Target id="@+id/btn_error_bank" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="102" startOffset="20" endLine="137" endOffset="71"/></Target><Target id="@+id/btn_report" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="148" startOffset="20" endLine="183" endOffset="71"/></Target><Target id="@+id/btn_practice" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="185" startOffset="20" endLine="220" endOffset="71"/></Target><Target id="@+id/tv_analyzed_count" view="TextView"><Expressions/><location startLine="265" startOffset="28" endLine="272" endOffset="58"/></Target><Target id="@+id/tv_error_count" view="TextView"><Expressions/><location startLine="290" startOffset="28" endLine="297" endOffset="58"/></Target><Target id="@+id/bottom_navigation" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="319" startOffset="4" endLine="327" endOffset="44"/></Target></Targets></Layout>