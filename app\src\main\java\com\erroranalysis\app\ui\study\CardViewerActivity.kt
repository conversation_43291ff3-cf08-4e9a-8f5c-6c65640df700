package com.erroranalysis.app.ui.study

import android.content.Intent
import android.os.Bundle
import android.speech.tts.TextToSpeech
import android.util.Log
import android.view.MenuItem
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.erroranalysis.app.R
import com.erroranalysis.app.databinding.ActivityCardViewerBinding
import com.erroranalysis.app.ui.base.ThemedActivity
import com.erroranalysis.app.ui.theme.AppTheme
import com.erroranalysis.app.data.DeckDataManager
import com.google.android.material.button.MaterialButton
import java.util.Locale

/**
 * 全新設計的卡片檢視Activity
 * 簡潔、可靠、易於維護
 */
class CardViewerActivity : ThemedActivity() {

    companion object {
        const val EXTRA_CARD = "extra_card"
        const val EXTRA_DECK_ID = "extra_deck_id"
        private const val TAG = "CardViewer"
        private const val REQUEST_EDIT_CARD = 1001
    }

    private lateinit var binding: ActivityCardViewerBinding
    private lateinit var dataManager: DeckDataManager
    private lateinit var card: StudyCard
    private lateinit var deckId: String

    // TTS語音播放
    private var textToSpeech: TextToSpeech? = null
    private var isTtsInitialized = false
    private var isTtsSpeaking = false

    // 當前顯示狀態：false=題目, true=答案+AI解答
    private var showingAnswer = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "CardViewerActivity 開始初始化")

        try {
            // 初始化視圖
            binding = ActivityCardViewerBinding.inflate(layoutInflater)
            setContentView(binding.root)
            Log.d(TAG, "視圖綁定完成")

            // 初始化數據管理器
            dataManager = DeckDataManager(this)

            // 獲取傳入的數據
            if (!extractIntentData()) {
                return // 數據獲取失敗，Activity已結束
            }

            // 設置界面
            setupToolbar()
            setupClickListeners()
            setupTTS()

            // 載入初始內容
            loadContent()

            // 應用主題
            applyTheme()

            Log.d(TAG, "CardViewerActivity 初始化完成")

        } catch (e: Exception) {
            Log.e(TAG, "初始化過程中發生錯誤", e)
            showError("初始化失敗：${e.message}")
            finish()
        }
    }

    /**
     * 從Intent中提取數據
     */
    private fun extractIntentData(): Boolean {
        return try {
            card = intent.getParcelableExtra(EXTRA_CARD) ?: run {
                Log.e(TAG, "無法獲取卡片數據")
                showError("無法載入卡片數據")
                finish()
                return false
            }

            deckId = intent.getStringExtra(EXTRA_DECK_ID) ?: run {
                Log.e(TAG, "無法獲取卡組ID")
                showError("無法載入卡組ID")
                finish()
                return false
            }

            Log.d(TAG, "數據提取成功 - 卡片ID: ${card.id}, 卡組ID: $deckId")
            true

        } catch (e: Exception) {
            Log.e(TAG, "提取Intent數據失敗", e)
            showError("數據載入失敗：${e.message}")
            finish()
            false
        }
    }

    /**
     * 設置工具列
     */
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "卡片檢視"
        }
    }

    /**
     * 設置點擊監聽器
     */
    private fun setupClickListeners() {
        // 內容區域點擊切換
        binding.contentFrame.setOnClickListener {
            toggleContent()
        }

        // 切換按鈕
        binding.btnToggle.setOnClickListener {
            toggleContent()
        }

        // 編輯按鈕
        binding.btnEdit.setOnClickListener {
            editCard()
        }

        // TTS語音播放按鈕
        binding.btnTts.setOnClickListener {
            toggleTTS()
        }
    }

    /**
     * 載入內容
     */
    private fun loadContent() {
        try {
            Log.d(TAG, "開始載入內容")
            
            // 顯示載入指示器
            binding.progressBar.visibility = android.view.View.VISIBLE
            
            // 載入題目內容
            showingAnswer = false
            displayCurrentContent()
            
            // 隱藏載入指示器
            binding.progressBar.visibility = android.view.View.GONE
            
            Log.d(TAG, "內容載入完成")

        } catch (e: Exception) {
            Log.e(TAG, "載入內容失敗", e)
            binding.progressBar.visibility = android.view.View.GONE
            showError("載入內容失敗：${e.message}")
        }
    }

    /**
     * 切換內容顯示
     */
    private fun toggleContent() {
        try {
            Log.d(TAG, "切換內容顯示")

            // 停止當前的TTS播放
            if (isTtsSpeaking) {
                stopTTS()
            }

            showingAnswer = !showingAnswer
            displayCurrentContent()

            // 滾動到頂部
            binding.scrollView.post {
                binding.scrollView.scrollTo(0, 0)
            }

        } catch (e: Exception) {
            Log.e(TAG, "切換內容失敗", e)
            showError("切換內容失敗：${e.message}")
        }
    }

    /**
     * 顯示當前內容
     */
    private fun displayCurrentContent() {
        if (showingAnswer) {
            displayAnswerContent()
        } else {
            displayQuestionContent()
        }
        updateUI()
    }

    /**
     * 顯示題目內容
     */
    private fun displayQuestionContent() {
        try {
            val content = extractTextContent(card.question)
            binding.tvContent.text = content
            Log.d(TAG, "題目內容顯示成功")
        } catch (e: Exception) {
            Log.e(TAG, "顯示題目失敗", e)
            binding.tvContent.text = "題目載入失敗：${e.message}"
        }
    }

    /**
     * 顯示答案內容
     */
    private fun displayAnswerContent() {
        try {
            val answerContent = extractTextContent(card.answer)
            val aiAnswerContent = if (card.aiAnswer.isNotEmpty()) {
                extractTextContent(card.aiAnswer)
            } else ""

            val combinedContent = buildString {
                append("📝 答案\n")
                append(answerContent)

                if (aiAnswerContent.isNotEmpty()) {
                    append("\n\n🤖 AI解答\n")
                    append(aiAnswerContent)
                }
            }

            binding.tvContent.text = combinedContent
            Log.d(TAG, "答案內容顯示成功")

        } catch (e: Exception) {
            Log.e(TAG, "顯示答案失敗", e)
            binding.tvContent.text = "答案載入失敗：${e.message}"
        }
    }

    /**
     * 從JSON或純文字中提取文字內容
     */
    private fun extractTextContent(content: String): String {
        return try {
            if (content.trim().startsWith("[")) {
                // JSON格式，提取文字內容
                val jsonArray = org.json.JSONArray(content)
                val textBuilder = StringBuilder()
                
                for (i in 0 until jsonArray.length()) {
                    val item = jsonArray.getJSONObject(i)
                    val type = item.getString("type")
                    val itemContent = item.getString("content")
                    
                    when (type) {
                        "text" -> {
                            textBuilder.append(itemContent)
                            if (i < jsonArray.length() - 1) {
                                textBuilder.append("\n")
                            }
                        }
                        "image" -> {
                            textBuilder.append("[圖片: $itemContent]")
                            if (i < jsonArray.length() - 1) {
                                textBuilder.append("\n")
                            }
                        }
                    }
                }
                
                textBuilder.toString()
            } else {
                // 純文字格式
                content
            }
        } catch (e: Exception) {
            Log.w(TAG, "JSON解析失敗，使用純文字", e)
            content
        }
    }

    /**
     * 更新UI狀態
     */
    private fun updateUI() {
        if (showingAnswer) {
            binding.tvContentType.text = if (card.aiAnswer.isNotEmpty()) "答案 & AI解答" else "答案"
            binding.tvTapHint.text = "點擊查看題目"
            binding.btnToggle.text = "查看題目"
            binding.btnToggle.icon = null
        } else {
            binding.tvContentType.text = "題目"
            val hintText = if (card.aiAnswer.isNotEmpty()) "點擊查看答案 & AI解答" else "點擊查看答案"
            binding.tvTapHint.text = hintText

            // 設置按鈕文字
            if (card.aiAnswer.isNotEmpty()) {
                binding.btnToggle.text = "查看答案 AI"
                binding.btnToggle.icon = null
                binding.btnToggle.maxLines = 1 // 強制單行
            } else {
                binding.btnToggle.text = "查看答案"
                binding.btnToggle.icon = null
                binding.btnToggle.maxLines = 1
            }
        }
    }

    /**
     * 編輯卡片
     */
    private fun editCard() {
        try {
            val intent = Intent(this, CardEditActivity::class.java)
            intent.putExtra(CardEditActivity.EXTRA_CARD, card)
            intent.putExtra(CardEditActivity.EXTRA_DECK_ID, deckId)
            startActivityForResult(intent, REQUEST_EDIT_CARD)
        } catch (e: Exception) {
            Log.e(TAG, "啟動編輯失敗", e)
            showError("無法開啟編輯功能：${e.message}")
        }
    }

    /**
     * 處理編輯結果
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == REQUEST_EDIT_CARD && resultCode == RESULT_OK) {
            val cardSaved = data?.getBooleanExtra(CardEditActivity.RESULT_CARD_SAVED, false) ?: false
            if (cardSaved) {
                // 重新載入卡片數據
                reloadCardData()
            }
        }
    }

    /**
     * 重新載入卡片數據
     */
    private fun reloadCardData() {
        try {
            // 從數據管理器重新載入卡片
            val allCards = dataManager.loadCards()
            val updatedCard = allCards.find { it.id == card.id }

            if (updatedCard != null) {
                card = updatedCard
                loadContent() // 重新載入內容到UI
                Log.d(TAG, "卡片數據已重新載入")
            } else {
                Log.w(TAG, "找不到更新後的卡片")
            }
        } catch (e: Exception) {
            Log.e(TAG, "重新載入卡片數據失敗", e)
            showError("載入更新後的卡片失敗：${e.message}")
        }
    }

    /**
     * 顯示錯誤信息
     */
    private fun showError(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
        Log.e(TAG, "錯誤: $message")
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onApplyTheme(theme: AppTheme) {
        // 應用主題到根佈局背景
        findViewById<android.view.View>(android.R.id.content).setBackgroundColor(theme.getBackgroundColorInt())

        // 應用主題到工具欄
        binding.toolbar.setBackgroundColor(theme.getPrimaryColorInt())
    }

    /**
     * 設置TTS語音播放
     */
    private fun setupTTS() {
        textToSpeech = TextToSpeech(this) { status ->
            if (status == TextToSpeech.SUCCESS) {
                isTtsInitialized = true
                // 設置語言，優先使用英文，如果不支援則使用預設語言
                val result = textToSpeech?.setLanguage(Locale.ENGLISH)
                if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                    // 如果英文不支援，嘗試使用繁體中文
                    textToSpeech?.setLanguage(Locale.TRADITIONAL_CHINESE)
                }
                Log.d(TAG, "TTS初始化成功")
            } else {
                isTtsInitialized = false
                Log.e(TAG, "TTS初始化失敗")
            }
        }
    }

    /**
     * 切換TTS播放/停止
     */
    private fun toggleTTS() {
        if (!isTtsInitialized || textToSpeech == null) {
            Toast.makeText(this, "語音功能尚未準備就緒", Toast.LENGTH_SHORT).show()
            return
        }

        if (isTtsSpeaking) {
            // 停止播放
            stopTTS()
        } else {
            // 開始播放
            speakCurrentContent()
        }
    }

    /**
     * 停止TTS播放
     */
    private fun stopTTS() {
        textToSpeech?.stop()
        isTtsSpeaking = false
        updateTTSButton()
        Toast.makeText(this, "語音播放已停止", Toast.LENGTH_SHORT).show()
    }

    /**
     * 播放當前內容的語音
     */
    private fun speakCurrentContent() {
        if (!isTtsInitialized || textToSpeech == null) {
            Toast.makeText(this, "語音功能尚未準備就緒", Toast.LENGTH_SHORT).show()
            return
        }

        val textToSpeak = if (showingAnswer) {
            // 播放答案內容，包含AI解答
            val answerText = extractTextContent(card.answer)
            val aiAnswerText = if (card.aiAnswer.isNotEmpty()) {
                "\n\nAI解答：${card.aiAnswer}"
            } else {
                ""
            }
            answerText + aiAnswerText
        } else {
            // 播放題目內容
            extractTextContent(card.question)
        }

        if (textToSpeak.isNotEmpty()) {
            // 檢測語言並設置相應的TTS語言
            val isEnglish = textToSpeak.matches(Regex(".*[a-zA-Z].*"))
            if (isEnglish) {
                textToSpeech?.setLanguage(Locale.ENGLISH)
            } else {
                textToSpeech?.setLanguage(Locale.TRADITIONAL_CHINESE)
            }

            // 設置播放完成監聽器
            val utteranceId = "tts_${System.currentTimeMillis()}"
            textToSpeech?.setOnUtteranceProgressListener(object : android.speech.tts.UtteranceProgressListener() {
                override fun onStart(utteranceId: String?) {
                    runOnUiThread {
                        isTtsSpeaking = true
                        updateTTSButton()
                    }
                }

                override fun onDone(utteranceId: String?) {
                    runOnUiThread {
                        isTtsSpeaking = false
                        updateTTSButton()
                    }
                }

                override fun onError(utteranceId: String?) {
                    runOnUiThread {
                        isTtsSpeaking = false
                        updateTTSButton()
                        Toast.makeText(this@CardViewerActivity, "語音播放出錯", Toast.LENGTH_SHORT).show()
                    }
                }
            })

            val params = Bundle()
            params.putString(TextToSpeech.Engine.KEY_PARAM_UTTERANCE_ID, utteranceId)
            textToSpeech?.speak(textToSpeak, TextToSpeech.QUEUE_FLUSH, params, utteranceId)

            Toast.makeText(this, "正在播放語音", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(this, "沒有可播放的文字內容", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 更新TTS按鈕圖標
     */
    private fun updateTTSButton() {
        if (isTtsSpeaking) {
            binding.btnTts.setImageResource(R.drawable.ic_volume_off)
            binding.btnTts.contentDescription = "停止語音播放"
        } else {
            binding.btnTts.setImageResource(R.drawable.ic_volume_up)
            binding.btnTts.contentDescription = "語音播放"
        }
    }



    override fun onDestroy() {
        super.onDestroy()
        // 釋放TTS資源
        textToSpeech?.stop()
        textToSpeech?.shutdown()
        isTtsSpeaking = false
    }
}
