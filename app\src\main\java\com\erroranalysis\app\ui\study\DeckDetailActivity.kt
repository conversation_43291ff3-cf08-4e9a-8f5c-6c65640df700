package com.erroranalysis.app.ui.study

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.erroranalysis.app.ui.base.ThemedActivity
import com.erroranalysis.app.ui.theme.AppTheme
import com.erroranalysis.app.R
import com.erroranalysis.app.databinding.ActivityDeckDetailBinding
import com.erroranalysis.app.databinding.DialogCardFilterBinding
import com.erroranalysis.app.data.DeckDataManager
import com.erroranalysis.app.ui.study.adapters.StudyCardAdapter
import com.erroranalysis.app.ui.widgets.RichTextEditText
import com.google.android.material.chip.Chip

/**
 * 卡組詳細頁面
 * 顯示卡組內的所有卡片，支援卡片的CRUD操作
 */
class DeckDetailActivity : ThemedActivity() {

    private lateinit var binding: ActivityDeckDetailBinding
    private lateinit var cardAdapter: StudyCardAdapter
    private lateinit var dataManager: DeckDataManager
    private lateinit var deck: SimpleDeck
    private lateinit var deckId: String
    private val cardList = mutableListOf<StudyCard>()
    private val filteredCardList = mutableListOf<StudyCard>()
    private var currentFilter = CardFilter()

    companion object {
        const val EXTRA_DECK = "extra_deck"
        const val REQUEST_CREATE_CARD = 1001
        const val REQUEST_EDIT_CARD = 1002
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDeckDetailBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 獲取傳入的卡組資料
        deck = intent.getParcelableExtra(EXTRA_DECK) ?: run {
            finish()
            return
        }

        // 初始化deckId
        deckId = deck.id

        // 初始化資料管理器
        dataManager = DeckDataManager(this)

        setupToolbar()
        setupRecyclerView()
        setupFab()
        setupFilterButton()
        loadCards()

        // 應用主題
        applyTheme()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayShowTitleEnabled(false) // 隱藏預設標題
            setDisplayHomeAsUpEnabled(true)
        }

        // 設置自定義標題
        binding.textDeckTitle.text = deck.name
        updateToolbarTitle()
    }

    private fun setupRecyclerView() {
        cardAdapter = StudyCardAdapter(
            onCardClick = { card -> viewCard(card) },
            onCardLongClick = { card -> showCardOptions(card) }
        )

        binding.recyclerCards.apply {
            layoutManager = LinearLayoutManager(this@DeckDetailActivity)
            adapter = cardAdapter
        }
    }

    private fun setupFab() {
        binding.fabCreateCard.setOnClickListener {
            showCreateCardDialog()
        }
    }

    private fun setupFilterButton() {
        binding.btnFilter.setOnClickListener {
            showFilterDialog()
        }
    }

    private fun loadCards() {
        cardList.clear()
        val loadedCards = dataManager.loadCardsByDeckId(deck.id)

        android.util.Log.d("DeckDetail", "載入卡組 ${deck.id} 的卡片，數量: ${loadedCards.size}")

        // 驗證所有卡片都屬於當前卡組
        val validCards = loadedCards.filter { it.deckId == deck.id }
        if (validCards.size != loadedCards.size) {
            android.util.Log.w("DeckDetail", "發現 ${loadedCards.size - validCards.size} 張不屬於當前卡組的卡片")
        }

        cardList.addAll(validCards)
        applyFilter()
        updateEmptyState()
        updateToolbarSubtitle()
    }

    private fun showCreateCardDialog() {
        val intent = Intent(this, CardEditActivity::class.java)
        intent.putExtra(CardEditActivity.EXTRA_DECK_ID, deck.id)
        startActivityForResult(intent, REQUEST_CREATE_CARD)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (resultCode == RESULT_OK) {
            when (requestCode) {
                REQUEST_CREATE_CARD, REQUEST_EDIT_CARD -> {
                    val cardSaved = data?.getBooleanExtra(CardEditActivity.RESULT_CARD_SAVED, false) ?: false
                    if (cardSaved) {
                        // 重新載入卡片列表
                        loadCards()
                    }
                }
            }
        }
    }

    private fun viewCard(card: StudyCard) {
        android.util.Log.e("DeckDetail", "=== 開始viewCard ===")
        android.util.Log.e("DeckDetail", "準備啟動CardViewActivity")
        android.util.Log.e("DeckDetail", "卡片ID: ${card.id}")
        android.util.Log.e("DeckDetail", "卡組ID: $deckId")
        android.util.Log.e("DeckDetail", "卡片題目: ${card.question.take(50)}")

        // 添加Toast提示確認點擊事件觸發
        android.widget.Toast.makeText(this, "正在開啟卡片：${card.id}", android.widget.Toast.LENGTH_SHORT).show()

        try {
            // 啟動全新的卡片檢視Activity
            val intent = Intent(this, CardViewerActivity::class.java)

            // 驗證Intent創建
            android.util.Log.e("DeckDetail", "Intent創建成功")

            // 添加數據
            intent.putExtra(CardViewerActivity.EXTRA_CARD, card)
            intent.putExtra(CardViewerActivity.EXTRA_DECK_ID, deckId)
            android.util.Log.e("DeckDetail", "Intent數據添加完成")

            // 驗證Activity類是否存在
            val componentName = intent.component
            android.util.Log.e("DeckDetail", "目標Activity: ${componentName?.className}")

            android.util.Log.e("DeckDetail", "準備調用startActivity")
            startActivity(intent)
            android.util.Log.e("DeckDetail", "startActivity調用完成")

        } catch (e: Exception) {
            android.util.Log.e("DeckDetail", "啟動CardViewActivity失敗", e)
            android.widget.Toast.makeText(this, "無法開啟卡片檢視：${e.message}", android.widget.Toast.LENGTH_LONG).show()
        }

        android.util.Log.e("DeckDetail", "=== viewCard結束 ===")
    }

    // 舊的對話框方法已刪除，現在使用CardViewActivity

    private fun showCardOptions(card: StudyCard) {
        val options = arrayOf("編輯", "刪除")

        AlertDialog.Builder(this)
            .setTitle("卡片選項")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> editCard(card)
                    1 -> deleteCard(card)
                }
            }
            .show()
    }

    private fun editCard(card: StudyCard) {
        val intent = Intent(this, CardEditActivity::class.java)
        intent.putExtra(CardEditActivity.EXTRA_DECK_ID, deck.id)
        intent.putExtra(CardEditActivity.EXTRA_CARD, card)
        startActivityForResult(intent, REQUEST_EDIT_CARD)
    }

    private fun deleteCard(card: StudyCard) {
        AlertDialog.Builder(this)
            .setTitle("刪除卡片")
            .setMessage("確定要刪除這張卡片嗎？")
            .setPositiveButton("刪除") { _, _ ->
                // 從資料管理器刪除
                dataManager.deleteCard(card.id)

                // 更新UI
                cardList.removeAll { it.id == card.id }
                cardAdapter.submitList(cardList.toList())
                updateEmptyState()
                updateToolbarSubtitle()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun updateEmptyState() {
        if (cardList.isEmpty()) {
            binding.layoutEmpty.visibility = android.view.View.VISIBLE
            binding.recyclerCards.visibility = android.view.View.GONE
        } else {
            binding.layoutEmpty.visibility = android.view.View.GONE
            binding.recyclerCards.visibility = android.view.View.VISIBLE
        }
    }

    private fun updateToolbarTitle() {
        binding.textCardCount.text = cardList.size.toString()
    }

    private fun updateToolbarSubtitle() {
        // 保留此方法以防其他地方調用
        updateToolbarTitle()
    }

    override fun onResume() {
        super.onResume()
        // 重新載入資料，確保資料同步
        loadCards()
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }

    /**
     * 顯示篩選對話框
     */
    private fun showFilterDialog() {
        val dialogBinding = DialogCardFilterBinding.inflate(layoutInflater)

        // 設置當前篩選條件
        dialogBinding.editKeyword.setText(currentFilter.keyword)

        // 設置熟練度選項
        setupMasteryChips(dialogBinding)

        // 設置標籤選項
        setupTagChips(dialogBinding)

        val dialog = AlertDialog.Builder(this)
            .setView(dialogBinding.root)
            .create()

        // 清除按鈕
        dialogBinding.btnClear.setOnClickListener {
            currentFilter = CardFilter()
            applyFilter()
            dialog.dismiss()
        }

        // 套用按鈕
        dialogBinding.btnApply.setOnClickListener {
            val keyword = dialogBinding.editKeyword.text.toString().trim()
            val selectedMastery = getSelectedMasteryLevels(dialogBinding)
            val selectedTags = getSelectedTags(dialogBinding)

            currentFilter = CardFilter(keyword, selectedMastery, selectedTags)
            applyFilter()
            dialog.dismiss()
        }

        dialog.show()
    }

    /**
     * 設置熟練度選項
     */
    private fun setupMasteryChips(dialogBinding: DialogCardFilterBinding) {
        val masteryChips = mapOf(
            dialogBinding.chipNotLearned to CardMastery.NOT_LEARNED,
            dialogBinding.chipBeginner to CardMastery.BEGINNER,
            dialogBinding.chipLearning to CardMastery.LEARNING,
            dialogBinding.chipFamiliar to CardMastery.FAMILIAR,
            dialogBinding.chipProficient to CardMastery.PROFICIENT,
            dialogBinding.chipMastered to CardMastery.MASTERED
        )

        masteryChips.forEach { (chip, mastery) ->
            chip.isChecked = currentFilter.selectedMasteryLevels.contains(mastery)
        }
    }

    /**
     * 設置標籤選項
     */
    private fun setupTagChips(dialogBinding: DialogCardFilterBinding) {
        val allTags = cardList.flatMap { it.tags }.distinct().sorted()

        dialogBinding.chipGroupTags.removeAllViews()

        allTags.forEach { tag ->
            val chip = Chip(this)
            chip.text = tag
            chip.isCheckable = true
            chip.isChecked = currentFilter.selectedTags.contains(tag)
            dialogBinding.chipGroupTags.addView(chip)
        }
    }

    /**
     * 獲取選中的熟練度
     */
    private fun getSelectedMasteryLevels(dialogBinding: DialogCardFilterBinding): Set<CardMastery> {
        val selected = mutableSetOf<CardMastery>()

        if (dialogBinding.chipNotLearned.isChecked) selected.add(CardMastery.NOT_LEARNED)
        if (dialogBinding.chipBeginner.isChecked) selected.add(CardMastery.BEGINNER)
        if (dialogBinding.chipLearning.isChecked) selected.add(CardMastery.LEARNING)
        if (dialogBinding.chipFamiliar.isChecked) selected.add(CardMastery.FAMILIAR)
        if (dialogBinding.chipProficient.isChecked) selected.add(CardMastery.PROFICIENT)
        if (dialogBinding.chipMastered.isChecked) selected.add(CardMastery.MASTERED)

        return selected
    }

    /**
     * 獲取選中的標籤
     */
    private fun getSelectedTags(dialogBinding: DialogCardFilterBinding): Set<String> {
        val selected = mutableSetOf<String>()

        for (i in 0 until dialogBinding.chipGroupTags.childCount) {
            val chip = dialogBinding.chipGroupTags.getChildAt(i) as Chip
            if (chip.isChecked) {
                selected.add(chip.text.toString())
            }
        }

        return selected
    }

    /**
     * 套用篩選條件
     */
    private fun applyFilter() {
        filteredCardList.clear()

        if (currentFilter.hasFilter()) {
            filteredCardList.addAll(cardList.filter { currentFilter.matches(it) })
        } else {
            filteredCardList.addAll(cardList)
        }

        cardAdapter.submitList(filteredCardList.toList())
        updateFilterIndicator()
    }

    /**
     * 更新篩選指示器
     */
    private fun updateFilterIndicator() {
        if (currentFilter.hasFilter()) {
            // 有篩選時：使用藍色圖標，顯示篩選數/總數
            binding.btnFilter.setImageResource(R.drawable.ic_filter_active)
            binding.textCardCount.text = "${filteredCardList.size}/${cardList.size}"
        } else {
            // 無篩選時：使用白色圖標，顯示總數
            binding.btnFilter.setImageResource(R.drawable.ic_filter_inactive)
            binding.textCardCount.text = cardList.size.toString()
        }
    }

    override fun onApplyTheme(theme: AppTheme) {
        // 應用主題到根佈局背景
        findViewById<android.view.View>(android.R.id.content).setBackgroundColor(theme.getBackgroundColorInt())

        // 應用主題到工具欄
        binding.toolbar.setBackgroundColor(theme.getPrimaryColorInt())

        // 應用主題到FAB
        binding.fabCreateCard.backgroundTintList =
            android.content.res.ColorStateList.valueOf(theme.getPrimaryColorInt())
    }
}
