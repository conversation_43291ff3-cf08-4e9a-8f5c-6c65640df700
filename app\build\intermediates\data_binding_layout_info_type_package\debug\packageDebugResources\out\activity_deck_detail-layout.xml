<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_deck_detail" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\activity_deck_detail.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_deck_detail_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="141" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="8" endLine="77" endOffset="43"/></Target><Target id="@+id/text_card_count" view="TextView"><Expressions/><location startLine="30" startOffset="20" endLine="39" endOffset="50"/></Target><Target id="@+id/text_deck_title" view="TextView"><Expressions/><location startLine="41" startOffset="20" endLine="49" endOffset="50"/></Target><Target id="@+id/btn_sort" view="ImageButton"><Expressions/><location startLine="54" startOffset="16" endLine="62" endOffset="57"/></Target><Target id="@+id/btn_filter" view="ImageButton"><Expressions/><location startLine="65" startOffset="16" endLine="73" endOffset="57"/></Target><Target id="@+id/recycler_cards" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="92" startOffset="12" endLine="95" endOffset="54"/></Target><Target id="@+id/layout_empty" view="LinearLayout"><Expressions/><location startLine="97" startOffset="12" endLine="127" endOffset="26"/></Target><Target id="@+id/fab_create_card" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="133" startOffset="4" endLine="139" endOffset="40"/></Target></Targets></Layout>