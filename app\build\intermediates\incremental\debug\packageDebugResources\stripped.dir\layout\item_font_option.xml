<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:background="?attr/selectableItemBackground"
    android:gravity="center_vertical">

    <!-- 選擇指示器 -->
    <RadioButton
        android:id="@+id/radio_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:buttonTint="@color/primary_blue" />

    <!-- 字體信息 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- 字體名稱 -->
        <TextView
            android:id="@+id/text_font_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="字體名稱"
            android:textSize="16sp"
            android:textColor="@color/text_primary"
            android:textStyle="bold" />

        <!-- 字體預覽 -->
        <TextView
            android:id="@+id/text_font_sample"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="字體預覽 Font Preview"
            android:textSize="14sp"
            android:textColor="@color/text_secondary" />

    </LinearLayout>

</LinearLayout>
