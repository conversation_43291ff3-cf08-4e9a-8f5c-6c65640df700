#Sun Jul 20 16:08:43 CST 2025
com.erroranalysis.app-main-5\:/color/bottom_nav_color.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\bottom_nav_color.xml
com.erroranalysis.app-main-5\:/drawable/bg_card_content.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_card_content.xml
com.erroranalysis.app-main-5\:/drawable/bg_hint_chip.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_hint_chip.xml
com.erroranalysis.app-main-5\:/drawable/bg_tag_rounded.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_tag_rounded.xml
com.erroranalysis.app-main-5\:/drawable/btn_crop_circle.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_crop_circle.xml
com.erroranalysis.app-main-5\:/drawable/btn_save_circle.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_save_circle.xml
com.erroranalysis.app-main-5\:/drawable/camera_overlay_border.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\camera_overlay_border.xml
com.erroranalysis.app-main-5\:/drawable/capture_button_background.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\capture_button_background.xml
com.erroranalysis.app-main-5\:/drawable/chip_background.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\chip_background.xml
com.erroranalysis.app-main-5\:/drawable/circle_background_primary.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_background_primary.xml
com.erroranalysis.app-main-5\:/drawable/circle_button_background.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_button_background.xml
com.erroranalysis.app-main-5\:/drawable/circle_button_inward.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_button_inward.xml
com.erroranalysis.app-main-5\:/drawable/circle_number_background.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_number_background.xml
com.erroranalysis.app-main-5\:/drawable/deck_cover_gradient.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\deck_cover_gradient.xml
com.erroranalysis.app-main-5\:/drawable/focus_indicator_capturing.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\focus_indicator_capturing.xml
com.erroranalysis.app-main-5\:/drawable/focus_indicator_too_close.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\focus_indicator_too_close.xml
com.erroranalysis.app-main-5\:/drawable/focus_indicator_too_far.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\focus_indicator_too_far.xml
com.erroranalysis.app-main-5\:/drawable/gradient_primary.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gradient_primary.xml
com.erroranalysis.app-main-5\:/drawable/ic_add.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add.xml
com.erroranalysis.app-main-5\:/drawable/ic_ai.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_ai.xml
com.erroranalysis.app-main-5\:/drawable/ic_ai_white.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_ai_white.xml
com.erroranalysis.app-main-5\:/drawable/ic_app_icon.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_app_icon.xml
com.erroranalysis.app-main-5\:/drawable/ic_archive.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_archive.xml
com.erroranalysis.app-main-5\:/drawable/ic_arrow_back.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrow_back.xml
com.erroranalysis.app-main-5\:/drawable/ic_book.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_book.xml
com.erroranalysis.app-main-5\:/drawable/ic_camera.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_camera.xml
com.erroranalysis.app-main-5\:/drawable/ic_chart.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_chart.xml
com.erroranalysis.app-main-5\:/drawable/ic_check.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_check.xml
com.erroranalysis.app-main-5\:/drawable/ic_check_circle.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_check_circle.xml
com.erroranalysis.app-main-5\:/drawable/ic_clock.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_clock.xml
com.erroranalysis.app-main-5\:/drawable/ic_close.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_close.xml
com.erroranalysis.app-main-5\:/drawable/ic_copy.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_copy.xml
com.erroranalysis.app-main-5\:/drawable/ic_crop_circle.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_crop_circle.xml
com.erroranalysis.app-main-5\:/drawable/ic_delete.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_delete.xml
com.erroranalysis.app-main-5\:/drawable/ic_edit.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_edit.xml
com.erroranalysis.app-main-5\:/drawable/ic_empty_deck.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_empty_deck.xml
com.erroranalysis.app-main-5\:/drawable/ic_expand_less.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_expand_less.xml
com.erroranalysis.app-main-5\:/drawable/ic_expand_more.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_expand_more.xml
com.erroranalysis.app-main-5\:/drawable/ic_export.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_export.xml
com.erroranalysis.app-main-5\:/drawable/ic_favorite_filled.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_favorite_filled.xml
com.erroranalysis.app-main-5\:/drawable/ic_filter.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_filter.xml
com.erroranalysis.app-main-5\:/drawable/ic_filter_active.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_filter_active.xml
com.erroranalysis.app-main-5\:/drawable/ic_filter_inactive.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_filter_inactive.xml
com.erroranalysis.app-main-5\:/drawable/ic_folder.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_folder.xml
com.erroranalysis.app-main-5\:/drawable/ic_home.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_home.xml
com.erroranalysis.app-main-5\:/drawable/ic_image.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_image.xml
com.erroranalysis.app-main-5\:/drawable/ic_info.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_info.xml
com.erroranalysis.app-main-5\:/drawable/ic_launcher_background.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.erroranalysis.app-main-5\:/drawable/ic_launcher_foreground.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_foreground.xml
com.erroranalysis.app-main-5\:/drawable/ic_lightbulb.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_lightbulb.xml
com.erroranalysis.app-main-5\:/drawable/ic_ocr.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_ocr.xml
com.erroranalysis.app-main-5\:/drawable/ic_palette.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_palette.xml
com.erroranalysis.app-main-5\:/drawable/ic_person.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_person.xml
com.erroranalysis.app-main-5\:/drawable/ic_photo_library.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_photo_library.xml
com.erroranalysis.app-main-5\:/drawable/ic_rotate_90.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_rotate_90.xml
com.erroranalysis.app-main-5\:/drawable/ic_save_circle.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_save_circle.xml
com.erroranalysis.app-main-5\:/drawable/ic_scissors.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_scissors.xml
com.erroranalysis.app-main-5\:/drawable/ic_search.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_search.xml
com.erroranalysis.app-main-5\:/drawable/ic_settings.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_settings.xml
com.erroranalysis.app-main-5\:/drawable/ic_sort.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_sort.xml
com.erroranalysis.app-main-5\:/drawable/ic_statistics.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_statistics.xml
com.erroranalysis.app-main-5\:/drawable/ic_volume_off.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_volume_off.xml
com.erroranalysis.app-main-5\:/drawable/ic_volume_up.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_volume_up.xml
com.erroranalysis.app-main-5\:/drawable/scrollbar_thumb.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\scrollbar_thumb.xml
com.erroranalysis.app-main-5\:/drawable/scrollbar_track.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\scrollbar_track.xml
com.erroranalysis.app-main-5\:/drawable/search_background.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\search_background.xml
com.erroranalysis.app-main-5\:/menu/bottom_navigation.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\bottom_navigation.xml
com.erroranalysis.app-main-5\:/menu/menu_main.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\menu_main.xml
com.erroranalysis.app-main-5\:/menu/menu_study_main.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\menu_study_main.xml
com.erroranalysis.app-main-5\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher.xml
com.erroranalysis.app-main-5\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.erroranalysis.app-main-5\:/xml/backup_rules.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.erroranalysis.app-main-5\:/xml/data_extraction_rules.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/activity_batch_import.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_batch_import.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/activity_camera.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_camera.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/activity_card_edit.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_card_edit.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/activity_card_viewer.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_card_viewer.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/activity_crop_overlay_test.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_crop_overlay_test.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/activity_deck_detail.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_deck_detail.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/activity_main.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/activity_photo_edit.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_photo_edit.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/activity_settings.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_settings.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/activity_simple_study.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_simple_study.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/dialog_card_filter.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_card_filter.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/dialog_create_card.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_create_card.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/dialog_create_deck.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_create_deck.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/dialog_create_deck_simple.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_create_deck_simple.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/dialog_simple_create_deck.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_simple_create_deck.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/item_color.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_color.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/item_icon_selector.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_icon_selector.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/item_quick_template.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_quick_template.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/item_simple_deck.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_simple_deck.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/item_study_card.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_study_card.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/item_template.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_template.xml
com.erroranalysis.app-packageDebugResources-2\:/layout/item_theme.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_theme.xml
