# 字體安裝指南

## 概述

本指南將詳細說明如何為ErrorAnalysisApp添加可商用字體文件。

## 步驟1：準備字體文件

### 推薦的免費可商用字體

#### 1. 思源黑體 (Noto Sans CJK)
- **下載地址**：https://fonts.google.com/noto/specimen/Noto+Sans+SC
- **許可證**：SIL Open Font License
- **文件大小**：約15-20MB
- **特點**：Google開發，支援繁簡中文、日文、韓文

#### 2. 辰宇落雁體
- **下載地址**：https://github.com/Chenyu-otf/chenyuluoyan_thin
- **許可證**：SIL Open Font License
- **文件大小**：約8-12MB
- **特點**：優雅的中文字體，適合正式文檔

#### 3. 清松手寫體 (霞鶩文楷)
- **下載地址**：https://github.com/lxgw/LxgwWenKai
- **許可證**：SIL Open Font License
- **文件大小**：約10-15MB
- **特點**：手寫風格，溫馨親切

## 步驟2：下載字體文件

### 方法1：直接下載TTF文件
1. 訪問上述GitHub或Google Fonts頁面
2. 下載.ttf或.otf格式的字體文件
3. 確保下載的是Regular（常規）版本

### 方法2：使用Git克隆
```bash
# 辰宇落雁體
git clone https://github.com/Chenyu-otf/chenyuluoyan_thin.git

# 清松手寫體
git clone https://github.com/lxgw/LxgwWenKai.git
```

## 步驟3：重命名字體文件

將下載的字體文件重命名為以下格式：

```
思源黑體：
- NotoSansCJK-Regular.ttf → noto_sans_cjk_regular.ttf
- NotoSansCJK-Bold.ttf → noto_sans_cjk_bold.ttf

辰宇落雁體：
- ChenyuLuoyan-Thin.ttf → chenyu_luoyan_regular.ttf

清松手寫體：
- LXGWWenKai-Regular.ttf → qingsong_handwriting_regular.ttf
```

## 步驟4：放置字體文件

將重命名後的字體文件複製到以下目錄：
```
ErrorAnalysisApp/app/src/main/res/font/
```

最終的目錄結構應該是：
```
app/src/main/res/font/
├── chenyu_luoyan.xml
├── chenyu_luoyan_regular.ttf
├── noto_sans_cjk.xml
├── noto_sans_cjk_regular.ttf
├── noto_sans_cjk_bold.ttf
├── qingsong_handwriting.xml
├── qingsong_handwriting_regular.ttf
└── font_family_config.xml
```

## 步驟5：編譯和測試

### 編譯APK
```bash
cd ErrorAnalysisApp
./gradlew assembleDebug
```

### 測試字體
1. 安裝編譯後的APK
2. 進入設置頁面
3. 點擊"字體選擇"
4. 選擇不同字體並查看預覽效果

## 注意事項

### 文件大小
- 中文字體文件通常較大（5-20MB）
- 會增加APK大小
- 建議只包含必要的字重

### 性能考慮
- 字體文件會在首次使用時載入
- 大字體文件可能影響應用啟動速度
- 應用會自動快取已載入的字體

### 版權合規
- 確保使用的字體具有商業使用許可
- 保留字體的版權聲明
- 遵守字體的使用條款

## 故障排除

### 字體無法顯示
1. 檢查文件名是否正確
2. 確認文件格式為.ttf或.otf
3. 檢查XML配置文件是否正確

### 編譯錯誤
1. 確認字體文件放在正確目錄
2. 檢查文件名中是否包含特殊字符
3. 確認XML文件格式正確

### APK過大
1. 考慮使用字體子集化工具
2. 只包含必要的字重
3. 壓縮字體文件

## 字體子集化（可選）

如果字體文件過大，可以使用工具創建子集：

### 使用fonttools
```bash
pip install fonttools
pyftsubset font.ttf --unicodes-file=chinese_chars.txt
```

### 使用在線工具
- Font Squirrel Webfont Generator
- Google Fonts Helper

## 更多字體推薦

### 其他免費可商用中文字體
- 阿里巴巴普惠體
- 站酷高端黑
- 站酷快樂體
- 思源宋體
- 文泉驛字體系列

### 獲取更多字體
- Google Fonts：https://fonts.google.com/
- GitHub開源字體項目
- 字體廠商官方網站
