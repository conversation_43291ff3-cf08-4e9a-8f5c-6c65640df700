/ Header Record For PersistentHashMapValueStorage android.app.Applicationi (androidx.appcompat.app.AppCompatActivity?com.erroranalysis.app.ui.theme.ThemeManager.ThemeChangeListener) (androidx.appcompat.app.AppCompatActivity androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum, +androidx.camera.core.ImageAnalysis.Analyzer) (androidx.appcompat.app.AppCompatActivity android.view.View kotlin.Enum android.view.View, +androidx.camera.core.ImageAnalysis.Analyzer, +androidx.camera.core.ImageAnalysis.Analyzer kotlin.Enum) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity- ,com.erroranalysis.app.ui.base.ThemedActivityi (androidx.appcompat.app.AppCompatActivity?com.erroranalysis.app.ui.theme.ThemeManager.ThemeChangeListener2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.appcompat.app.AppCompatActivity- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity android.os.Parcelable android.os.Parcelable kotlin.Enum kotlin.Enum kotlin.Enum) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.appcompat.app.AppCompatActivity8 7com.google.android.material.textfield.TextInputEditText android.text.style.ImageSpan) (com.erroranalysis.app.utils.ImportResult) (com.erroranalysis.app.utils.ImportResult2 1com.erroranalysis.app.utils.DocumentProcessResult2 1com.erroranalysis.app.utils.DocumentProcessResult9 8androidx.recyclerview.widget.RecyclerView.ItemDecoration!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity- ,com.erroranalysis.app.ui.base.ThemedActivityi (androidx.appcompat.app.AppCompatActivity?com.erroranalysis.app.ui.theme.ThemeManager.ThemeChangeListener- ,com.erroranalysis.app.ui.base.ThemedActivity android.os.Parcelable!  androidx.viewbinding.ViewBinding- ,com.erroranalysis.app.ui.base.ThemedActivityi (androidx.appcompat.app.AppCompatActivity?com.erroranalysis.app.ui.theme.ThemeManager.ThemeChangeListener- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity android.os.Parcelablei (androidx.appcompat.app.AppCompatActivity?com.erroranalysis.app.ui.theme.ThemeManager.ThemeChangeListener2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity android.os.Parcelable) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity- ,com.erroranalysis.app.ui.base.ThemedActivityi (androidx.appcompat.app.AppCompatActivity?com.erroranalysis.app.ui.theme.ThemeManager.ThemeChangeListener- ,com.erroranalysis.app.ui.base.ThemedActivity android.os.Parcelable) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback!  androidx.viewbinding.ViewBinding) (androidx.appcompat.app.AppCompatActivity- ,com.erroranalysis.app.ui.base.ThemedActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity android.view.View kotlin.Enum android.view.View- ,com.erroranalysis.app.ui.base.ThemedActivityi (androidx.appcompat.app.AppCompatActivity?com.erroranalysis.app.ui.theme.ThemeManager.ThemeChangeListener- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity android.os.Parcelable) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.appcompat.app.AppCompatActivity- ,com.erroranalysis.app.ui.base.ThemedActivity) (androidx.appcompat.app.AppCompatActivity- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity android.os.Parcelable) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback!  androidx.viewbinding.ViewBinding- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity android.os.Parcelable- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity