<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="160dp"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="20dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@color/white"
    app:strokeWidth="1dp"
    app:strokeColor="@color/border_light">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="12dp">

        <!-- 主要圖標 -->
        <TextView
            android:id="@+id/text_cover_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="12dp"
            android:text="📘"
            android:textSize="42sp" />

        <!-- 卡組名稱 -->
        <TextView
            android:id="@+id/text_deck_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/text_cover_icon"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="12dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="數學"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- 卡片數量 - 右下角圓形背景 -->
        <FrameLayout
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:background="@drawable/circle_background_primary">

            <TextView
                android:id="@+id/text_card_count_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="0"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:textStyle="bold" />

        </FrameLayout>

        <!-- 隱藏的卡片數量文字（保持兼容性） -->
        <TextView
            android:id="@+id/text_card_count"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

    </RelativeLayout>

</com.google.android.material.card.MaterialCardView>
