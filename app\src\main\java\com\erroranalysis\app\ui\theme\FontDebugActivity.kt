package com.erroranalysis.app.ui.theme

import android.os.Bundle
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.erroranalysis.app.R

/**
 * 字體調試Activity
 * 用於測試字體載入是否正常
 */
class FontDebugActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 創建簡單的測試界面
        val textView = TextView(this).apply {
            text = "字體測試\n辰宇落雁體測試\nFont Test\n1234567890"
            textSize = 18f
            setPadding(32, 32, 32, 32)
        }
        
        setContentView(textView)
        
        // 輸出診斷信息
        val diagnosis = FontManager.diagnoseFontIssues(this)
        android.util.Log.d("FontDebug", diagnosis)
        
        // 嘗試載入辰宇落雁體
        val typeface = FontManager.getTypeface(this, FontManager.FontType.CHENYU_LUOYAN)
        if (typeface != null) {
            textView.typeface = typeface
            android.util.Log.d("FontDebug", "字體設置成功")
        } else {
            android.util.Log.e("FontDebug", "字體載入失敗")
            textView.text = textView.text.toString() + "\n\n字體載入失敗！"
        }
    }
}
