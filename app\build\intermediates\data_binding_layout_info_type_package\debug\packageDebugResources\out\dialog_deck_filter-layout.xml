<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_deck_filter" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\dialog_deck_filter.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_deck_filter_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="142" endOffset="14"/></Target><Target id="@+id/edit_keyword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="26" startOffset="8" endLine="33" endOffset="59"/></Target><Target id="@+id/chip_group_star" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="47" startOffset="4" endLine="71" endOffset="48"/></Target><Target id="@+id/chip_starred" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="55" startOffset="8" endLine="61" endOffset="53"/></Target><Target id="@+id/chip_not_starred" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="63" startOffset="8" endLine="69" endOffset="53"/></Target><Target id="@+id/chip_group_card_count" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="83" startOffset="4" endLine="115" endOffset="48"/></Target><Target id="@+id/chip_empty" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="91" startOffset="8" endLine="97" endOffset="53"/></Target><Target id="@+id/chip_few_cards" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="99" startOffset="8" endLine="105" endOffset="53"/></Target><Target id="@+id/chip_many_cards" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="107" startOffset="8" endLine="113" endOffset="53"/></Target><Target id="@+id/btn_clear" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="124" startOffset="8" endLine="131" endOffset="55"/></Target><Target id="@+id/btn_apply" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="133" startOffset="8" endLine="138" endOffset="58"/></Target></Targets></Layout>