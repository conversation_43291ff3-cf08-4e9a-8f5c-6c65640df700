package com.erroranalysis.app.ui.study

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 學習卡片資料模型
 */
@Parcelize
data class StudyCard(
    val id: String,
    val deckId: String,
    val question: String,
    val answer: String,
    val aiAnswer: String = "", // AI解答內容
    val questionImagePath: String? = null,
    val answerImagePath: String? = null,
    val tags: List<String> = emptyList(),
    val difficulty: CardDifficulty = CardDifficulty.NORMAL, // 保留向後兼容
    val mastery: CardMastery = CardMastery.NOT_LEARNED, // 新的熟練度系統
    val masteryLevel: Int = 0, // 0-5，0表示未掌握，5表示完全掌握（保留向後兼容）
    val reviewCount: Int = 0,
    val correctCount: Int = 0,
    val lastReviewTime: Long = 0,
    val nextReviewTime: Long = 0,
    val createdTime: Long = System.currentTimeMillis(),
    val updatedTime: Long = System.currentTimeMillis(),
    val isStarred: Boolean = false
) : Parcelable {
    
    /**
     * 計算正確率
     */
    val accuracyRate: Float
        get() = if (reviewCount > 0) correctCount.toFloat() / reviewCount else 0f
    
    /**
     * 是否需要複習
     */
    val needsReview: Boolean
        get() = System.currentTimeMillis() >= nextReviewTime
    
    /**
     * 是否已掌握
     */
    val isMastered: Boolean
        get() = masteryLevel >= 4
}

/**
 * 卡片熟練度枚舉
 */
enum class CardMastery(val displayName: String, val color: String, val level: Int) {
    NOT_LEARNED("未學習", "#9E9E9E", 0),
    BEGINNER("初學", "#F44336", 1),
    LEARNING("學習中", "#FF9800", 2),
    FAMILIAR("熟悉", "#2196F3", 3),
    PROFICIENT("熟練", "#4CAF50", 4),
    MASTERED("精通", "#8BC34A", 5)
}

/**
 * 向後兼容的難度枚舉（保留以支援舊數據）
 */
@Deprecated("請使用CardMastery代替")
enum class CardDifficulty(val displayName: String, val color: String) {
    EASY("簡單", "#4CAF50"),
    NORMAL("普通", "#2196F3"),
    HARD("困難", "#FF9800"),
    VERY_HARD("很困難", "#F44336");

    /**
     * 轉換為新的熟練度系統
     */
    fun toMastery(): CardMastery {
        return when (this) {
            EASY -> CardMastery.PROFICIENT
            NORMAL -> CardMastery.FAMILIAR
            HARD -> CardMastery.LEARNING
            VERY_HARD -> CardMastery.BEGINNER
        }
    }
}

/**
 * 複習結果枚舉
 */
enum class ReviewResult {
    AGAIN,      // 再次複習
    HARD,       // 困難
    GOOD,       // 良好
    EASY        // 簡單
}
