#Sun Jul 20 14:08:01 CST 2025
com.erroranalysis.app-main-47\:/color/bottom_nav_color.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_bottom_nav_color.xml.flat
com.erroranalysis.app-main-47\:/drawable/bg_card_content.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_card_content.xml.flat
com.erroranalysis.app-main-47\:/drawable/bg_hint_chip.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_hint_chip.xml.flat
com.erroranalysis.app-main-47\:/drawable/bg_tag_rounded.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_tag_rounded.xml.flat
com.erroranalysis.app-main-47\:/drawable/btn_crop_circle.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_crop_circle.xml.flat
com.erroranalysis.app-main-47\:/drawable/btn_save_circle.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_save_circle.xml.flat
com.erroranalysis.app-main-47\:/drawable/camera_overlay_border.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_camera_overlay_border.xml.flat
com.erroranalysis.app-main-47\:/drawable/capture_button_background.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_capture_button_background.xml.flat
com.erroranalysis.app-main-47\:/drawable/chip_background.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_chip_background.xml.flat
com.erroranalysis.app-main-47\:/drawable/circle_background_primary.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_background_primary.xml.flat
com.erroranalysis.app-main-47\:/drawable/circle_button_background.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_button_background.xml.flat
com.erroranalysis.app-main-47\:/drawable/circle_button_inward.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_button_inward.xml.flat
com.erroranalysis.app-main-47\:/drawable/circle_number_background.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_number_background.xml.flat
com.erroranalysis.app-main-47\:/drawable/deck_cover_gradient.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_deck_cover_gradient.xml.flat
com.erroranalysis.app-main-47\:/drawable/focus_indicator_capturing.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_focus_indicator_capturing.xml.flat
com.erroranalysis.app-main-47\:/drawable/focus_indicator_too_close.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_focus_indicator_too_close.xml.flat
com.erroranalysis.app-main-47\:/drawable/focus_indicator_too_far.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_focus_indicator_too_far.xml.flat
com.erroranalysis.app-main-47\:/drawable/gradient_primary.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_gradient_primary.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_add.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_ai.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_ai.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_ai_white.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_ai_white.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_app_icon.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_app_icon.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_archive.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_archive.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_arrow_back.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_back.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_book.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_book.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_camera.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_camera.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_chart.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_chart.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_check.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_check.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_check_circle.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_check_circle.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_clock.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_clock.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_close.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_close.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_copy.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_copy.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_crop_circle.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_crop_circle.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_delete.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_delete.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_edit.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_edit.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_empty_deck.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_empty_deck.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_expand_less.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_expand_less.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_expand_more.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_expand_more.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_export.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_export.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_favorite_filled.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_favorite_filled.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_filter.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_filter.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_filter_active.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_filter_active.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_filter_inactive.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_filter_inactive.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_folder.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_folder.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_home.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_home.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_image.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_image.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_info.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_info.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_launcher_background.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_launcher_foreground.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_lightbulb.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_lightbulb.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_ocr.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_ocr.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_palette.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_palette.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_person.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_person.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_photo_library.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_photo_library.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_rotate_90.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_rotate_90.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_save_circle.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_save_circle.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_scissors.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_scissors.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_search.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_search.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_settings.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_settings.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_sort.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_sort.xml.flat
com.erroranalysis.app-main-47\:/drawable/ic_statistics.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_statistics.xml.flat
com.erroranalysis.app-main-47\:/drawable/scrollbar_thumb.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_scrollbar_thumb.xml.flat
com.erroranalysis.app-main-47\:/drawable/scrollbar_track.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_scrollbar_track.xml.flat
com.erroranalysis.app-main-47\:/drawable/search_background.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_search_background.xml.flat
com.erroranalysis.app-main-47\:/menu/bottom_navigation.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_bottom_navigation.xml.flat
com.erroranalysis.app-main-47\:/menu/menu_main.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_main.xml.flat
com.erroranalysis.app-main-47\:/menu/menu_study_main.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_study_main.xml.flat
com.erroranalysis.app-main-47\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.erroranalysis.app-main-47\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.erroranalysis.app-main-47\:/xml/backup_rules.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.erroranalysis.app-main-47\:/xml/data_extraction_rules.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/activity_batch_import.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_batch_import.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/activity_camera.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_camera.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/activity_card_edit.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_card_edit.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/activity_card_viewer.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_card_viewer.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/activity_crop_overlay_test.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_crop_overlay_test.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/activity_deck_detail.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_deck_detail.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/activity_main.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/activity_photo_edit.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_photo_edit.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/activity_settings.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_settings.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/activity_simple_study.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_simple_study.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/dialog_card_filter.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_card_filter.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/dialog_create_card.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_create_card.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/dialog_create_deck.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_create_deck.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/dialog_create_deck_simple.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_create_deck_simple.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/dialog_simple_create_deck.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_simple_create_deck.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/item_color.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_color.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/item_icon_selector.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_icon_selector.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/item_quick_template.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_quick_template.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/item_simple_deck.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_simple_deck.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/item_study_card.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_study_card.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/item_template.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_template.xml.flat
com.erroranalysis.app-mergeDebugResources-44\:/layout/item_theme.xml=C\:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_theme.xml.flat
