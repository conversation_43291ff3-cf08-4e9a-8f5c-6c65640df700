package com.erroranalysis.app.ui.main

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.erroranalysis.app.databinding.ActivityMainBinding
import com.erroranalysis.app.ui.base.ThemedActivity
import com.erroranalysis.app.ui.theme.AppTheme
import com.erroranalysis.app.ui.camera.CameraActivity
import com.erroranalysis.app.ui.camera.SimpleCameraActivity
import com.erroranalysis.app.ui.camera.CropOverlayTestActivity
import com.erroranalysis.app.ui.study.SimpleStudyActivity
import com.erroranalysis.app.ui.study.DeckDetailActivity
import com.erroranalysis.app.ui.settings.SettingsActivity
import com.erroranalysis.app.utils.OpenCVManager
import com.erroranalysis.app.data.DeckDataManager

/**
 * 簡化版主畫面Activity
 * 提供應用程式的主要功能入口
 */
class SimpleMainActivity : ThemedActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var dataManager: DeckDataManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 初始化資料管理器
        dataManager = DeckDataManager(this)

        // 初始化OpenCV
        OpenCVManager.initialize(this) { success ->
            if (success) {
                setupUI()
            } else {
                Toast.makeText(this, "OpenCV 初始化失敗", Toast.LENGTH_LONG).show()
                setupUI() // 即使失敗也繼續設置UI
            }
        }
    }
    
    private fun setupUI() {
        setupClickListeners()
        updateStatsUI()

        // 應用主題
        applyTheme()
    }
    
    private fun setupClickListeners() {
        // 主功能按鈕
        binding.btnCamera.setOnClickListener {
            startActivity(Intent(this, CameraActivity::class.java))
        }
        
        // 移除不存在的按鈕
        
        // 記憶庫按鈕 - 直接跳轉到隨手記卡組
        binding.btnErrorBank.setOnClickListener {
            openQuickNotesDeck()
        }

        // 報告按鈕
        binding.btnReport.setOnClickListener {
            Toast.makeText(this, "學習報告功能開發中", Toast.LENGTH_SHORT).show()
        }

        // 練習按鈕
        binding.btnPractice.setOnClickListener {
            Toast.makeText(this, "練習推薦功能開發中", Toast.LENGTH_SHORT).show()
        }
        
        // 底部導航
        binding.bottomNavigation.setOnItemSelectedListener { item ->
            when (item.itemId) {
                com.erroranalysis.app.R.id.nav_home -> {
                    // 已在主頁
                    true
                }
                com.erroranalysis.app.R.id.nav_camera -> {
                    startActivity(Intent(this, CameraActivity::class.java))
                    true
                }
                com.erroranalysis.app.R.id.nav_error_bank -> {
                    openQuickNotesDeck()
                    true
                }
                com.erroranalysis.app.R.id.nav_settings -> {
                    startActivity(Intent(this, SettingsActivity::class.java))
                    true
                }
                // 移除不存在的導航項目
                else -> false
            }
        }
    }
    
    private fun updateStatsUI() {
        // 簡化版本，不更新不存在的UI元素
        // 可以在這裡添加實際存在的UI更新
    }
    
    override fun onResume() {
        super.onResume()
        // 重新載入數據
        updateStatsUI()
        // 重新應用主題（從設置頁面返回時）
        applyTheme()
    }

    override fun onApplyTheme(theme: AppTheme) {
        // 應用主題到根佈局背景
        findViewById<android.view.View>(android.R.id.content).setBackgroundColor(theme.getBackgroundColorInt())

        // 應用主題到頂部區域 - 使用漸變效果
        val headerLayout = binding.layoutHeader
        val gradientDrawable = android.graphics.drawable.GradientDrawable(
            android.graphics.drawable.GradientDrawable.Orientation.LEFT_RIGHT,
            intArrayOf(theme.getGradientStartColorInt(), theme.getGradientEndColorInt())
        )
        headerLayout.background = gradientDrawable

        // 應用主題到底部導航欄
        binding.bottomNavigation.setBackgroundColor(theme.getCardBackgroundColorInt())

        // 應用主題到功能按鈕
        applyThemeToFunctionButtons(theme)

        // 強制重新繪製
        binding.root.invalidate()
    }

    private fun applyThemeToFunctionButtons(theme: AppTheme) {
        // 創建漸變背景的函數
        fun createGradientDrawable(): android.graphics.drawable.GradientDrawable {
            val gradient = android.graphics.drawable.GradientDrawable(
                android.graphics.drawable.GradientDrawable.Orientation.LEFT_RIGHT,
                intArrayOf(theme.getGradientStartColorInt(), theme.getGradientEndColorInt())
            )
            gradient.cornerRadius = 16f
            return gradient
        }

        // 直接找到每個按鈕內部的LinearLayout並應用主題
        try {
            // 相機按鈕
            val cameraLayout = binding.btnCamera.getChildAt(0) as? android.widget.LinearLayout
            cameraLayout?.background = createGradientDrawable()

            // 記憶庫按鈕
            val errorBankLayout = binding.btnErrorBank.getChildAt(0) as? android.widget.LinearLayout
            errorBankLayout?.background = createGradientDrawable()

            // 報告按鈕
            val reportLayout = binding.btnReport.getChildAt(0) as? android.widget.LinearLayout
            reportLayout?.background = createGradientDrawable()

            // 練習按鈕
            val practiceLayout = binding.btnPractice.getChildAt(0) as? android.widget.LinearLayout
            practiceLayout?.background = createGradientDrawable()

        } catch (e: Exception) {
            // 如果出現異常，記錄日誌但不影響應用運行
            android.util.Log.w("SimpleMainActivity", "應用主題到功能按鈕時出現異常: ${e.message}")
        }
    }

    /**
     * 打開隨手記卡組
     */
    private fun openQuickNotesDeck() {
        try {
            // 確保隨手記卡組存在
            val quickNotesDeck = dataManager.getQuickNotesDeck()

            if (quickNotesDeck != null) {
                // 直接跳轉到隨手記卡組詳情頁面
                val intent = Intent(this, DeckDetailActivity::class.java)
                intent.putExtra(DeckDetailActivity.EXTRA_DECK, quickNotesDeck)
                startActivity(intent)
            } else {
                // 如果隨手記卡組不存在，跳轉到記憶庫主頁面
                Toast.makeText(this, "正在初始化隨手記...", Toast.LENGTH_SHORT).show()
                startActivity(Intent(this, SimpleStudyActivity::class.java))
            }
        } catch (e: Exception) {
            // 出錯時跳轉到記憶庫主頁面
            android.util.Log.e("SimpleMainActivity", "打開隨手記失敗", e)
            startActivity(Intent(this, SimpleStudyActivity::class.java))
        }
    }
}
