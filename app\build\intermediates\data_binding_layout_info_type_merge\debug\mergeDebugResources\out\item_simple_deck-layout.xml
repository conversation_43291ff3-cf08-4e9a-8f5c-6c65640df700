<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_simple_deck" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\item_simple_deck.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_simple_deck_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="73" endOffset="51"/></Target><Target id="@+id/text_cover_icon" view="TextView"><Expressions/><location startLine="20" startOffset="8" endLine="27" endOffset="37"/></Target><Target id="@+id/text_deck_name" view="TextView"><Expressions/><location startLine="30" startOffset="8" endLine="42" endOffset="38"/></Target><Target id="@+id/text_card_count_number" view="TextView"><Expressions/><location startLine="52" startOffset="12" endLine="60" endOffset="42"/></Target><Target id="@+id/text_card_count" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="69" endOffset="39"/></Target></Targets></Layout>