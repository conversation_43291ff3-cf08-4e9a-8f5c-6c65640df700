#!/usr/bin/env python3
"""
字體下載腳本
自動下載免費可商用字體到Android項目中
"""

import os
import requests
import shutil
from pathlib import Path

# 字體下載配置
FONTS_CONFIG = {
    "noto_sans_cjk": {
        "name": "思源黑體",
        "urls": {
            "regular": "https://github.com/googlefonts/noto-cjk/raw/main/Sans/OTF/SimplifiedChinese/NotoSansCJKsc-Regular.otf",
            "bold": "https://github.com/googlefonts/noto-cjk/raw/main/Sans/OTF/SimplifiedChinese/NotoSansCJKsc-Bold.otf"
        },
        "target_names": {
            "regular": "noto_sans_cjk_regular.ttf",
            "bold": "noto_sans_cjk_bold.ttf"
        }
    },
    "lxgw_wenkai": {
        "name": "霞鶩文楷（清松手寫體）",
        "urls": {
            "regular": "https://github.com/lxgw/LxgwWenKai/releases/download/v1.330/LXGWWenKai-Regular.ttf"
        },
        "target_names": {
            "regular": "qingsong_handwriting_regular.ttf"
        }
    }
}

def download_file(url, target_path):
    """下載文件"""
    print(f"正在下載: {url}")
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(target_path, 'wb') as f:
            shutil.copyfileobj(response.raw, f)
        
        print(f"下載完成: {target_path}")
        return True
    except Exception as e:
        print(f"下載失敗: {e}")
        return False

def main():
    """主函數"""
    # 確定字體目錄
    font_dir = Path("app/src/main/res/font")
    font_dir.mkdir(parents=True, exist_ok=True)
    
    print("開始下載字體文件...")
    print("=" * 50)
    
    for font_key, font_config in FONTS_CONFIG.items():
        print(f"\n正在處理: {font_config['name']}")
        print("-" * 30)
        
        for weight, url in font_config["urls"].items():
            target_name = font_config["target_names"][weight]
            target_path = font_dir / target_name
            
            # 檢查文件是否已存在
            if target_path.exists():
                print(f"文件已存在，跳過: {target_name}")
                continue
            
            # 下載文件
            success = download_file(url, target_path)
            if not success:
                print(f"警告: {target_name} 下載失敗")
    
    print("\n" + "=" * 50)
    print("字體下載完成！")
    print("\n注意事項:")
    print("1. 某些字體可能需要手動下載")
    print("2. 請確認字體文件的使用許可")
    print("3. 大字體文件會增加APK大小")
    print("\n下一步:")
    print("1. 運行 ./gradlew assembleDebug 編譯項目")
    print("2. 在設置中測試字體功能")

if __name__ == "__main__":
    main()
