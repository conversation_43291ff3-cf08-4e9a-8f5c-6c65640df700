<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_card_viewer" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\activity_card_viewer.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_card_viewer_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="163" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="15" startOffset="8" endLine="22" endOffset="55"/></Target><Target id="@+id/tv_content_type" view="TextView"><Expressions/><location startLine="35" startOffset="8" endLine="43" endOffset="53"/></Target><Target id="@+id/btn_tts" view="ImageButton"><Expressions/><location startLine="46" startOffset="8" endLine="55" endOffset="44"/></Target><Target id="@+id/tv_tap_hint" view="TextView"><Expressions/><location startLine="57" startOffset="8" endLine="66" endOffset="43"/></Target><Target id="@+id/content_frame" view="FrameLayout"><Expressions/><location startLine="71" startOffset="4" endLine="123" endOffset="17"/></Target><Target id="@+id/scroll_view" view="ScrollView"><Expressions/><location startLine="83" startOffset="8" endLine="113" endOffset="20"/></Target><Target id="@+id/tv_content" view="TextView"><Expressions/><location startLine="99" startOffset="16" endLine="109" endOffset="63"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="116" startOffset="8" endLine="121" endOffset="39"/></Target><Target id="@+id/btn_edit" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="134" startOffset="8" endLine="144" endOffset="51"/></Target><Target id="@+id/btn_toggle" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="146" startOffset="8" endLine="159" endOffset="51"/></Target></Targets></Layout>