<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":opencv" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\sdk\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="com.google.mlkit:text-recognition-chinese:16.0.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets"><file name="mlkit-google-ocr-models/aksara/aksara_page_layout_analysis_rpn_gcn.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\aksara\aksara_page_layout_analysis_rpn_gcn.binarypb"/><file name="mlkit-google-ocr-models/aksara/aksara_page_layout_analysis_ti_rpn_gcn.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\aksara\aksara_page_layout_analysis_ti_rpn_gcn.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Hani_ctc/optical/assets.extra/LabelMap.pb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Hani_ctc\optical\assets.extra\LabelMap.pb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Hani_ctc/optical/conv_model.fb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Hani_ctc\optical\conv_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Hani_ctc/optical/lstm_model.fb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Hani_ctc\optical\lstm_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Hani_ctc_cpu.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Hani_ctc_cpu.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/assets.extra/LabelMap.pb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\assets.extra\LabelMap.pb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/conv_model.fb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\conv_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/lstm_model.fb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\lstm_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc_cpu.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc_cpu.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/tflite_langid.tflite" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\tflite_langid.tflite"/><file name="mlkit-google-ocr-models/gocr/layout/line_clustering_custom_ops/model.tflite" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\layout\line_clustering_custom_ops\model.tflite"/><file name="mlkit-google-ocr-models/gocr/layout/line_splitting_custom_ops/model.tflite" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\gocr\layout\line_splitting_custom_ops\model.tflite"/><file name="mlkit-google-ocr-models/taser/detector/region_proposal_text_detector_tflite_vertical_mbv2_v1.bincfg" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser\detector\region_proposal_text_detector_tflite_vertical_mbv2_v1.bincfg"/><file name="mlkit-google-ocr-models/taser/detector/rpn_text_detector_mobile_space_to_depth_quantized_mbv2_v1.tflite" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser\detector\rpn_text_detector_mobile_space_to_depth_quantized_mbv2_v1.tflite"/><file name="mlkit-google-ocr-models/taser/rpn_text_detection_tflite_mobile_mbv2.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser\rpn_text_detection_tflite_mobile_mbv2.binarypb"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.bincfg" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.bincfg"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.conv_model" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.conv_model"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.lstm_model" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.lstm_model"/><file name="mlkit-google-ocr-models/taser/taser_script_identification_tflite_mobile.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser\taser_script_identification_tflite_mobile.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_engine.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_engine.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_engine_ti.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_engine_ti.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_recognizer.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_recognizer.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_runner.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_runner.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_runner_ti.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrchinese_and_latin_mbv2_aksara_layout_gcn_mobile_runner_ti.binarypb"/></source></dataSet><dataSet config="com.google.mlkit:text-recognition:16.0.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets"><file name="mlkit-google-ocr-models/aksara/aksara_page_layout_analysis_rpn_gcn.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\aksara\aksara_page_layout_analysis_rpn_gcn.binarypb"/><file name="mlkit-google-ocr-models/aksara/aksara_page_layout_analysis_ti_rpn_gcn.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\aksara\aksara_page_layout_analysis_ti_rpn_gcn.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/assets.extra/LabelMap.pb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\assets.extra\LabelMap.pb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/conv_model.fb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\conv_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/lstm_model.fb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\lstm_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc_cpu.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc_cpu.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/tflite_langid.tflite" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\tflite_langid.tflite"/><file name="mlkit-google-ocr-models/gocr/layout/line_clustering_custom_ops/model.tflite" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\layout\line_clustering_custom_ops\model.tflite"/><file name="mlkit-google-ocr-models/gocr/layout/line_splitting_custom_ops/model.tflite" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\layout\line_splitting_custom_ops\model.tflite"/><file name="mlkit-google-ocr-models/taser/detector/region_proposal_text_detector_tflite_vertical_mbv2_v1.bincfg" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\detector\region_proposal_text_detector_tflite_vertical_mbv2_v1.bincfg"/><file name="mlkit-google-ocr-models/taser/detector/rpn_text_detector_mobile_space_to_depth_quantized_mbv2_v1.tflite" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\detector\rpn_text_detector_mobile_space_to_depth_quantized_mbv2_v1.tflite"/><file name="mlkit-google-ocr-models/taser/rpn_text_detection_tflite_mobile_mbv2.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\rpn_text_detection_tflite_mobile_mbv2.binarypb"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.bincfg" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.bincfg"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.conv_model" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.conv_model"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.lstm_model" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.lstm_model"/><file name="mlkit-google-ocr-models/taser/taser_script_identification_tflite_mobile.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\taser_script_identification_tflite_mobile.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_engine.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_engine.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_engine_ti.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_engine_ti.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_recognizer.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_recognizer.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_runner.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_runner.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_runner_ti.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_runner_ti.binarypb"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\assets"><file name="fonts/README.txt" path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\assets\fonts\README.txt"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>