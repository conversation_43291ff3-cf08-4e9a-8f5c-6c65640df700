<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_simple_study" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\activity_simple_study.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_simple_study_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="133" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="8" endLine="15" endOffset="74"/></Target><Target id="@+id/edit_search" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="49" startOffset="20" endLine="56" endOffset="71"/></Target><Target id="@+id/btn_filter" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="61" startOffset="16" endLine="69" endOffset="84"/></Target><Target id="@+id/btn_sort" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="72" startOffset="16" endLine="79" endOffset="84"/></Target><Target id="@+id/recycler_decks" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="83" startOffset="12" endLine="86" endOffset="54"/></Target><Target id="@+id/layout_empty" view="LinearLayout"><Expressions/><location startLine="88" startOffset="12" endLine="118" endOffset="26"/></Target><Target id="@+id/fab_create_deck" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="125" startOffset="4" endLine="131" endOffset="40"/></Target></Targets></Layout>