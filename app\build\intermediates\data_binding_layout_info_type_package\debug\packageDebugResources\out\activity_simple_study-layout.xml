<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_simple_study" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\activity_simple_study.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_simple_study_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="131" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="8" endLine="15" endOffset="74"/></Target><Target id="@+id/edit_search" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="49" startOffset="20" endLine="54" endOffset="51"/></Target><Target id="@+id/btn_filter" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="59" startOffset="16" endLine="67" endOffset="84"/></Target><Target id="@+id/btn_sort" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="70" startOffset="16" endLine="77" endOffset="84"/></Target><Target id="@+id/recycler_decks" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="81" startOffset="12" endLine="84" endOffset="54"/></Target><Target id="@+id/layout_empty" view="LinearLayout"><Expressions/><location startLine="86" startOffset="12" endLine="116" endOffset="26"/></Target><Target id="@+id/fab_create_deck" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="123" startOffset="4" endLine="129" endOffset="40"/></Target></Targets></Layout>